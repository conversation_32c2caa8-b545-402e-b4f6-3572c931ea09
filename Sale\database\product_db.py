"""
产品数据库管理模块
"""
import sqlite3
import json
from typing import List, Dict, Optional
from dataclasses import dataclass

@dataclass
class Product:
    """产品数据类"""
    id: int
    name: str
    price: float
    design_description: str
    material: str
    target_customer: str
    stock: int
    features: str

class ProductDatabase:
    """产品数据库管理类"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库和示例数据"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建产品表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                design_description TEXT NOT NULL,
                material TEXT NOT NULL,
                target_customer TEXT NOT NULL,
                stock INTEGER NOT NULL,
                features TEXT NOT NULL
            )
        ''')
        
        # 检查是否已有数据
        cursor.execute('SELECT COUNT(*) FROM products')
        if cursor.fetchone()[0] == 0:
            self._insert_sample_data(cursor)
        
        conn.commit()
        conn.close()
    
    def _insert_sample_data(self, cursor):
        """插入示例产品数据"""
        sample_products = [
            (
                "经典圆环手镯",
                1299.0,
                "简约圆环设计，表面光滑，适合日常佩戴",
                "999足金",
                "预算敏感性",
                50,
                "经典款式,日常佩戴,性价比高"
            ),
            (
                "花丝工艺手镯",
                3999.0,
                "传统花丝工艺，镂空雕花设计，工艺精湛",
                "999足金",
                "追求独特设计型",
                15,
                "传统工艺,独特设计,限量款"
            ),
            (
                "简约开口手镯",
                899.0,
                "开口设计，可调节大小，简约时尚",
                "999足金",
                "犹豫不决型",
                80,
                "可调节,简约,入门款"
            ),
            (
                "龙凤呈祥手镯",
                5999.0,
                "龙凤图案雕刻，寓意吉祥，婚庆首选",
                "999足金",
                "追求独特设计型",
                10,
                "婚庆款,寓意吉祥,高端定制"
            ),
            (
                "心形镂空手镯",
                2199.0,
                "心形镂空设计，浪漫优雅，适合年轻女性",
                "999足金",
                "追求独特设计型",
                25,
                "浪漫设计,年轻时尚,情侣款"
            ),
            (
                "福字手镯",
                1599.0,
                "福字雕刻，寓意福气满满，老少皆宜",
                "999足金",
                "犹豫不决型",
                40,
                "寓意吉祥,老少皆宜,保值"
            ),
            (
                "竹节手镯",
                1899.0,
                "竹节造型，寓意节节高升，质感厚重",
                "999足金",
                "预算敏感性",
                35,
                "寓意美好,质感厚重,中等价位"
            ),
            (
                "蝴蝶飞舞手镯",
                4599.0,
                "立体蝴蝶造型，活动设计，栩栩如生",
                "999足金",
                "追求独特设计型",
                8,
                "立体造型,活动设计,艺术品级"
            ),
            (
                "平安扣手镯",
                1099.0,
                "平安扣元素，寓意平安健康，经典款式",
                "999足金",
                "犹豫不决型",
                60,
                "寓意平安,经典款式,大众喜爱"
            ),
            (
                "玫瑰花手镯",
                2799.0,
                "玫瑰花造型，层次丰富，浪漫典雅",
                "999足金",
                "追求独特设计型",
                20,
                "浪漫典雅,层次丰富,女性专属"
            )
        ]
        
        cursor.executemany('''
            INSERT INTO products (name, price, design_description, material, target_customer, stock, features)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', sample_products)
    
    def get_all_products(self) -> List[Product]:
        """获取所有产品"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM products')
        rows = cursor.fetchall()
        
        products = []
        for row in rows:
            products.append(Product(*row))
        
        conn.close()
        return products
    
    def get_products_by_customer_type(self, customer_type: str) -> List[Product]:
        """根据客户类型获取推荐产品"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'SELECT * FROM products WHERE target_customer = ? AND stock > 0',
            (customer_type,)
        )
        rows = cursor.fetchall()
        
        products = []
        for row in rows:
            products.append(Product(*row))
        
        conn.close()
        return products
    
    def get_products_by_price_range(self, min_price: float, max_price: float) -> List[Product]:
        """根据价格范围获取产品"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'SELECT * FROM products WHERE price BETWEEN ? AND ? AND stock > 0',
            (min_price, max_price)
        )
        rows = cursor.fetchall()
        
        products = []
        for row in rows:
            products.append(Product(*row))
        
        conn.close()
        return products
    
    def get_product_by_id(self, product_id: int) -> Optional[Product]:
        """根据ID获取产品"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('SELECT * FROM products WHERE id = ?', (product_id,))
        row = cursor.fetchone()
        
        conn.close()
        
        if row:
            return Product(*row)
        return None
    
    def update_stock(self, product_id: int, new_stock: int):
        """更新库存"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute(
            'UPDATE products SET stock = ? WHERE id = ?',
            (new_stock, product_id)
        )
        
        conn.commit()
        conn.close()
