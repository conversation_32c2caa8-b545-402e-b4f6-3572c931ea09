"""
简化演示脚本 - 不依赖外部LLM
"""
import os
import sys
import sqlite3

def test_basic_functionality():
    """测试基本功能"""
    print("🛍️ 黄金手镯销售模拟系统 - 简化演示")
    print("=" * 60)
    
    # 测试产品数据库
    print("\n📦 测试产品数据库...")
    try:
        from database.product_db import ProductDatabase
        
        db = ProductDatabase("demo_products.db")
        products = db.get_all_products()
        
        print(f"✅ 成功加载 {len(products)} 个产品")
        
        # 显示几个产品示例
        print("\n🏷️ 产品示例:")
        for i, product in enumerate(products[:3]):
            print(f"{i+1}. {product.name} - {product.price}元")
            print(f"   {product.design_description}")
        
        # 测试按客户类型筛选
        budget_products = db.get_products_by_customer_type("预算敏感性")
        design_products = db.get_products_by_customer_type("追求独特设计型")
        hesitant_products = db.get_products_by_customer_type("犹豫不决型")
        
        print(f"\n📊 产品分布:")
        print(f"预算敏感型: {len(budget_products)} 个")
        print(f"设计追求型: {len(design_products)} 个")
        print(f"犹豫不决型: {len(hesitant_products)} 个")
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        return False
    
    # 测试提示模板
    print("\n📝 测试提示模板...")
    try:
        from prompts.customer_prompts import CUSTOMER_PROMPTS, CUSTOMER_GREETINGS
        from prompts.sales_prompts import SALES_TECHNIQUES, SALES_GREETING
        
        print("✅ 客户类型提示模板:")
        for customer_type in CUSTOMER_PROMPTS.keys():
            print(f"  - {customer_type}")
        
        print("✅ 销售技巧模板:")
        for technique in SALES_TECHNIQUES.keys():
            print(f"  - {technique}")
        
    except Exception as e:
        print(f"❌ 提示模板测试失败: {e}")
        return False
    
    # 模拟对话场景
    print("\n🎭 模拟对话场景...")
    try:
        scenarios = [
            {
                "customer_type": "预算敏感性",
                "customer_message": "你好，我想看看黄金手镯，有什么性价比高的推荐吗？",
                "sales_response": "欢迎光临！我为您推荐几款性价比很高的黄金手镯。这款经典圆环手镯只要1299元，999足金材质，简约大方，非常适合日常佩戴。"
            },
            {
                "customer_type": "追求独特设计型", 
                "customer_message": "我在找一些设计独特的黄金手镯，有什么特别的款式吗？",
                "sales_response": "您真有眼光！我特别推荐这款花丝工艺手镯，采用传统花丝工艺，镂空雕花设计非常精美，这种工艺现在很少见了，绝对独一无二。"
            },
            {
                "customer_type": "犹豫不决型",
                "customer_message": "我想买个黄金手镯，但不太确定选哪款，你能帮我推荐一下吗？",
                "sales_response": "当然可以！根据您的需求，我建议您看看这款简约开口手镯，可以调节大小，而且款式经典，适合各种场合。我们还提供7天无理由退换，您可以放心选择。"
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n场景 {i}: {scenario['customer_type']}")
            print("-" * 40)
            print(f"👤 客户: {scenario['customer_message']}")
            print(f"🛍️  销售员: {scenario['sales_response']}")
        
    except Exception as e:
        print(f"❌ 对话场景测试失败: {e}")
        return False
    
    # 清理测试文件
    if os.path.exists("demo_products.db"):
        os.remove("demo_products.db")
    
    print("\n🎉 基本功能测试完成！")
    print("\n📋 系统特点:")
    print("✅ 三种客户类型智能体")
    print("✅ 四种销售技巧模板") 
    print("✅ 完整的产品数据库")
    print("✅ LangGraph工作流管理")
    print("✅ 对话记录和分析")
    
    print("\n🚀 下一步:")
    print("1. 安装依赖: pip install -r requirements.txt")
    print("2. 配置API密钥在config.py中")
    print("3. 运行完整版本: python main.py")
    
    return True

def show_architecture():
    """显示系统架构"""
    print("\n🏗️ 系统架构")
    print("=" * 60)
    
    architecture = """
📊 核心组件:

1. 客户智能体 (Customer Agents)
   🎯 预算敏感型 - 关注价格和性价比
   🎨 追求独特设计型 - 关注设计和工艺  
   🤔 犹豫不决型 - 需要建议和保证

2. 销售智能体 (Sales Agent)
   🔍 需求探寻 - 了解客户需求
   💎 产品推荐 - 匹配合适产品
   🛡️ 异议处理 - 解决客户疑虑
   🤝 成交缔结 - 促成交易

3. LangGraph工作流
   📋 状态管理 - 跟踪对话状态
   🔄 流程控制 - 管理对话流程
   ⚖️ 条件判断 - 决定下一步行动

4. 产品数据库
   💾 SQLite存储 - 10款黄金手镯
   🏷️ 分类筛选 - 按类型、价格筛选
   📦 库存管理 - 实时库存跟踪

5. 对话管理
   📝 记录历史 - 完整对话记录
   📈 统计分析 - 成功率分析
   🎯 性能评估 - 销售效果评估
"""
    
    print(architecture)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--architecture":
        show_architecture()
    else:
        success = test_basic_functionality()
        if success:
            print("\n✨ 系统准备就绪！")
        else:
            print("\n⚠️ 系统测试失败，请检查错误信息。")
