"""
智能购买意向和满意度分析器
"""
import re
import json
from typing import Dict, <PERSON><PERSON>, Optional
from prompts.intent_analysis_prompts import (
    PURCHASE_INTENT_ANALYSIS_PROMPT,
    SATISFACTION_ANALYSIS_PROMPT,
    COMPREHENSIVE_ANALYSIS_PROMPT,
    CUSTOMER_TYPE_ADJUSTMENTS
)

class IntentAnalyzer:
    """智能意向分析器"""
    
    def __init__(self, llm):
        self.llm = llm
        
    def analyze_intent_and_satisfaction(
        self,
        customer_type: str,
        conversation_round: int,
        current_intent: float,
        current_satisfaction: float,
        sales_message: str,
        customer_response: str,
        conversation_history: list
    ) -> Tuple[float, float]:
        """
        分析购买意向和满意度
        
        Returns:
            Tuple[float, float]: (新的购买意向, 新的满意度)
        """
        
        # 构建对话历史字符串
        history_str = "\n".join(conversation_history[-6:]) if conversation_history else "无"
        
        # 分析购买意向
        new_intent = self._analyze_purchase_intent(
            customer_type, conversation_round, current_intent,
            sales_message, customer_response, history_str
        )
        
        # 分析满意度
        new_satisfaction = self._analyze_satisfaction(
            customer_type, conversation_round, current_satisfaction,
            sales_message, customer_response, history_str
        )
        
        # 应用对话前期的中立原则
        if conversation_round <= 2:
            new_intent = self._apply_early_conversation_neutrality(new_intent, current_intent)
            new_satisfaction = self._apply_early_conversation_neutrality(new_satisfaction, current_satisfaction)
        
        # 确保值在有效范围内
        new_intent = max(0.0, min(1.0, new_intent))
        new_satisfaction = max(0.0, min(1.0, new_satisfaction))
        
        return new_intent, new_satisfaction
    
    def _analyze_purchase_intent(
        self,
        customer_type: str,
        conversation_round: int,
        current_intent: float,
        sales_message: str,
        customer_response: str,
        history_str: str
    ) -> float:
        """分析购买意向"""
        
        prompt = PURCHASE_INTENT_ANALYSIS_PROMPT.format(
            customer_type=customer_type,
            conversation_round=conversation_round,
            current_intent=current_intent,
            sales_message=sales_message,
            customer_response=customer_response,
            conversation_history=history_str
        )
        
        try:
            response = self.llm.invoke(prompt)
            # 提取数值
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)
            
            # 使用正则表达式提取数值
            numbers = re.findall(r'0\.\d+|1\.0|0|1', response_text)
            if numbers:
                intent_value = float(numbers[0])
                return max(0.0, min(1.0, intent_value))
            else:
                # 如果无法解析，使用规则基础的后备方案
                return self._fallback_intent_analysis(customer_response, current_intent)
                
        except Exception as e:
            print(f"购买意向分析失败: {e}")
            return self._fallback_intent_analysis(customer_response, current_intent)
    
    def _analyze_satisfaction(
        self,
        customer_type: str,
        conversation_round: int,
        current_satisfaction: float,
        sales_message: str,
        customer_response: str,
        history_str: str
    ) -> float:
        """分析满意度"""
        
        prompt = SATISFACTION_ANALYSIS_PROMPT.format(
            customer_type=customer_type,
            conversation_round=conversation_round,
            current_satisfaction=current_satisfaction,
            sales_message=sales_message,
            customer_response=customer_response,
            conversation_history=history_str
        )
        
        try:
            response = self.llm.invoke(prompt)
            # 提取数值
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)
            
            # 使用正则表达式提取数值
            numbers = re.findall(r'0\.\d+|1\.0|0|1', response_text)
            if numbers:
                satisfaction_value = float(numbers[0])
                return max(0.0, min(1.0, satisfaction_value))
            else:
                # 如果无法解析，使用规则基础的后备方案
                return self._fallback_satisfaction_analysis(customer_response, current_satisfaction)
                
        except Exception as e:
            print(f"满意度分析失败: {e}")
            return self._fallback_satisfaction_analysis(customer_response, current_satisfaction)
    
    def _apply_early_conversation_neutrality(self, new_value: float, current_value: float) -> float:
        """应用对话前期的中立原则"""
        # 在对话前期，除非有强烈信号，否则保持中立
        if new_value < 0.3 or new_value > 0.7:
            # 允许强烈的正面或负面信号
            return new_value
        else:
            # 对于中等信号，向中立值调整
            neutral_range = (0.5, 0.5)
            if new_value < neutral_range[0]:
                return max(new_value, neutral_range[0])
            elif new_value > neutral_range[1]:
                return min(new_value, neutral_range[1])
            else:
                return new_value
    
    def _fallback_intent_analysis(self, customer_response: str, current_intent: float) -> float:
        """后备的购买意向分析（基于关键词）"""
        # 强烈购买信号
        strong_buy_signals = ["我要", "我买", "成交", "购买", "下单", "付款"]
        # 积极信号
        positive_signals = ["不错", "喜欢", "考虑", "可以", "感兴趣", "这个好"]
        # 消极信号
        negative_signals = ["不要", "不买", "算了", "太贵", "不合适", "再看看"]
        # 强烈拒绝信号
        strong_negative_signals = ["绝对不", "不可能", "离开", "浪费时间"]
        
        if any(signal in customer_response for signal in strong_buy_signals):
            return min(1.0, current_intent + 0.3)
        elif any(signal in customer_response for signal in positive_signals):
            return min(1.0, current_intent + 0.1)
        elif any(signal in customer_response for signal in strong_negative_signals):
            return max(0.0, current_intent - 0.3)
        elif any(signal in customer_response for signal in negative_signals):
            return max(0.0, current_intent - 0.1)
        else:
            return current_intent
    
    def _fallback_satisfaction_analysis(self, customer_response: str, current_satisfaction: float) -> float:
        """后备的满意度分析（基于关键词）"""
        # 高满意度信号
        high_satisfaction_signals = ["很好", "专业", "满意", "谢谢", "不错", "赞"]
        # 积极信号
        positive_signals = ["好的", "可以", "明白", "了解", "清楚"]
        # 消极信号
        negative_signals = ["不好", "不满", "失望", "糟糕", "不专业"]
        # 强烈不满信号
        strong_negative_signals = ["太差", "愤怒", "投诉", "欺骗"]
        
        if any(signal in customer_response for signal in high_satisfaction_signals):
            return min(1.0, current_satisfaction + 0.2)
        elif any(signal in customer_response for signal in positive_signals):
            return min(1.0, current_satisfaction + 0.05)
        elif any(signal in customer_response for signal in strong_negative_signals):
            return max(0.0, current_satisfaction - 0.3)
        elif any(signal in customer_response for signal in negative_signals):
            return max(0.0, current_satisfaction - 0.1)
        else:
            return current_satisfaction
    
    def get_comprehensive_analysis(
        self,
        customer_type: str,
        conversation_round: int,
        current_intent: float,
        current_satisfaction: float,
        sales_message: str,
        customer_response: str,
        conversation_history: list
    ) -> Dict:
        """获取综合分析结果（包含详细信息）"""
        
        history_str = "\n".join(conversation_history[-6:]) if conversation_history else "无"
        
        prompt = COMPREHENSIVE_ANALYSIS_PROMPT.format(
            customer_type=customer_type,
            conversation_round=conversation_round,
            current_intent=current_intent,
            current_satisfaction=current_satisfaction,
            sales_message=sales_message,
            customer_response=customer_response,
            conversation_history=history_str
        )
        
        try:
            response = self.llm.invoke(prompt)
            if hasattr(response, 'content'):
                response_text = response.content
            else:
                response_text = str(response)
            
            # 尝试解析JSON
            try:
                result = json.loads(response_text)
                # 确保数值在有效范围内
                result["purchase_intent"] = max(0.0, min(1.0, result.get("purchase_intent", current_intent)))
                result["satisfaction"] = max(0.0, min(1.0, result.get("satisfaction", current_satisfaction)))
                return result
            except json.JSONDecodeError:
                # JSON解析失败，使用简单分析
                new_intent, new_satisfaction = self.analyze_intent_and_satisfaction(
                    customer_type, conversation_round, current_intent, current_satisfaction,
                    sales_message, customer_response, conversation_history
                )
                return {
                    "purchase_intent": new_intent,
                    "satisfaction": new_satisfaction,
                    "analysis_reason": "使用简化分析方法",
                    "key_signals": []
                }
                
        except Exception as e:
            print(f"综合分析失败: {e}")
            # 返回当前值
            return {
                "purchase_intent": current_intent,
                "satisfaction": current_satisfaction,
                "analysis_reason": f"分析失败: {str(e)}",
                "key_signals": []
            }
