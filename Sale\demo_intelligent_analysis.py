"""
智能购买意向和满意度分析演示
"""
import sys

def create_enhanced_mock_llm():
    """创建增强的模拟LLM，支持智能分析"""
    
    class EnhancedMockLLM:
        def __init__(self):
            self.conversation_count = 0
            
            # 客户回应模板
            self.customer_responses = {
                "预算敏感性": [
                    "你好，我想看看黄金手镯，有什么性价比高的推荐吗？",
                    "这个价格怎么样？有没有更便宜一点的？",
                    "1500元左右的有吗？我预算不是很多。",
                    "这款看起来不错，能便宜点吗？",
                    "好的，我考虑一下这款。"
                ],
                "追求独特设计型": [
                    "你好，我在找一些设计独特的黄金手镯，有什么特别的款式吗？",
                    "这个设计有什么特别之处？工艺怎么样？",
                    "有没有限量款或者可以定制的？",
                    "这个花丝工艺很精美，我很喜欢。",
                    "就要这款花丝工艺手镯了。"
                ],
                "犹豫不决型": [
                    "你好，我想买个黄金手镯，但不太确定选哪款，你能帮我推荐一下吗？",
                    "你觉得哪款更适合我？我有点选择困难。",
                    "如果买了不合适怎么办？可以退换吗？",
                    "你推荐哪个？我真的不知道怎么选。",
                    "好吧，我相信你的推荐，就要这个。"
                ]
            }
            
            self.current_customer_type = None
            self.response_index = 0
        
        def invoke(self, prompt):
            # 智能分析提示词处理
            if "购买意向" in prompt:
                return self._analyze_purchase_intent(prompt)
            elif "满意度" in prompt:
                return self._analyze_satisfaction(prompt)
            else:
                # 客户对话回应
                return self._generate_customer_response(prompt)
        
        def _analyze_purchase_intent(self, prompt):
            """分析购买意向"""
            # 提取关键信息
            if "我买了" in prompt or "成交" in prompt or "就要这个" in prompt:
                return MockResponse("0.9")
            elif "太贵" in prompt or "不要" in prompt or "算了" in prompt:
                return MockResponse("0.2")
            elif "考虑" in prompt or "不错" in prompt or "喜欢" in prompt:
                return MockResponse("0.7")
            elif "对话轮数：1" in prompt or "对话轮数：2" in prompt:
                # 对话前期保持中立
                return MockResponse("0.5")
            else:
                return MockResponse("0.6")
        
        def _analyze_satisfaction(self, prompt):
            """分析满意度"""
            if "很好" in prompt or "专业" in prompt or "精美" in prompt:
                return MockResponse("0.8")
            elif "不满" in prompt or "糟糕" in prompt or "不专业" in prompt:
                return MockResponse("0.3")
            elif "可以" in prompt or "明白" in prompt or "了解" in prompt:
                return MockResponse("0.6")
            elif "对话轮数：1" in prompt or "对话轮数：2" in prompt:
                # 对话前期保持中立
                return MockResponse("0.5")
            else:
                return MockResponse("0.6")
        
        def _generate_customer_response(self, prompt):
            """生成客户回应"""
            if self.current_customer_type and self.current_customer_type in self.customer_responses:
                responses = self.customer_responses[self.current_customer_type]
                if self.response_index < len(responses):
                    response = responses[self.response_index]
                    self.response_index += 1
                    return MockResponse(response)
            
            return MockResponse("好的，我了解了。")
    
    class MockResponse:
        def __init__(self, content):
            self.content = content
    
    return EnhancedMockLLM()

def demo_intelligent_analysis():
    """演示智能分析功能"""
    print("🧠 智能购买意向和满意度分析演示")
    print("=" * 60)
    print("本演示展示如何使用LLM智能判断客户的购买意向和满意度")
    print("特点：对话前期保持中立，随着对话深入动态调整")
    print("=" * 60)
    
    try:
        from simple_workflow import SimpleSalesWorkflow
        from utils.conversation_manager import ConversationManager
        
        # 创建增强的模拟LLM
        enhanced_llm = create_enhanced_mock_llm()
        
        # 初始化组件
        workflow = SimpleSalesWorkflow(enhanced_llm, "demo_intelligent.db")
        conversation_manager = ConversationManager("intelligent_demo_logs.json")
        
        # 客户类型
        customer_types = ["预算敏感性", "追求独特设计型", "犹豫不决型"]
        
        print("\n🎭 开始演示三种客户类型的智能分析场景...\n")
        
        for i, customer_type in enumerate(customer_types, 1):
            print(f"\n{'='*20} 场景 {i}: {customer_type} {'='*20}")
            
            # 设置当前客户类型
            enhanced_llm.current_customer_type = customer_type
            enhanced_llm.response_index = 0
            
            try:
                # 运行模拟
                result = workflow.run_simulation(customer_type)
                
                # 显示对话和分析结果
                print("\n💬 对话过程:")
                print("-" * 40)
                for j, message in enumerate(result["conversation"], 1):
                    if message.startswith("销售员: "):
                        print(f"{j}. 🛍️  {message}")
                    elif message.startswith("客户: "):
                        print(f"{j}. 👤 {message}")
                    else:
                        print(f"{j}.    {message}")
                
                # 显示智能分析结果
                print(f"\n📊 智能分析结果:")
                print("-" * 40)
                customer_status = result["customer_final_status"]
                print(f"客户类型: {customer_status['customer_type']}")
                print(f"最终购买意向: {customer_status['purchase_intent']:.2f}")
                print(f"最终满意度: {customer_status['satisfaction']:.2f}")
                print(f"对话轮数: {customer_status['conversation_rounds']}")
                print(f"分析方法: {customer_status.get('analysis_method', 'LLM智能分析')}")
                
                # 分析结果解读
                intent = customer_status['purchase_intent']
                satisfaction = customer_status['satisfaction']
                
                print(f"\n🔍 结果解读:")
                if intent >= 0.8:
                    print("✅ 强烈购买意向 - 客户很可能会购买")
                elif intent >= 0.6:
                    print("🟡 积极购买意向 - 客户有一定购买倾向")
                elif intent >= 0.4:
                    print("🟠 中立态度 - 客户还在观望中")
                else:
                    print("❌ 消极态度 - 客户购买可能性较低")
                
                if satisfaction >= 0.8:
                    print("😊 非常满意 - 客户对服务高度认可")
                elif satisfaction >= 0.6:
                    print("🙂 比较满意 - 客户对服务基本认可")
                elif satisfaction >= 0.4:
                    print("😐 中立态度 - 客户对服务无明显倾向")
                else:
                    print("😞 不满意 - 客户对服务有意见")
                
                # 显示最终结果
                status = "✅ 成功交易" if result["success"] else "❌ 未成交"
                print(f"\n🎯 最终结果: {status}")
                
                # 保存结果
                conversation_manager.save_conversation(result)
                
            except Exception as e:
                print(f"❌ 演示失败: {e}")
            
            if i < len(customer_types):
                input("\n按回车键继续下一个场景...")
        
        print("\n" + "="*60)
        print("🎉 智能分析演示完成！")
        
        # 显示统计
        conversation_manager.print_statistics()
        
        # 显示智能分析的优势
        print("\n💡 智能分析的优势:")
        print("✅ 基于LLM的语义理解，比关键词匹配更准确")
        print("✅ 考虑对话上下文和客户类型特征")
        print("✅ 对话前期保持中立，避免过早判断")
        print("✅ 动态调整，随着对话深入提供更精确的分析")
        print("✅ 提供规则基础的后备方案，确保系统稳定性")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_analysis_comparison():
    """展示智能分析与传统方法的对比"""
    print("\n📊 智能分析 vs 传统关键词匹配对比")
    print("=" * 60)
    
    comparison_cases = [
        {
            "scenario": "客户说：'这个设计很有意思，但我需要再想想'",
            "traditional": "关键词匹配：检测到'想想' → 购买意向下降",
            "intelligent": "智能分析：理解客户对设计感兴趣但需要时间决策 → 购买意向保持或略升"
        },
        {
            "scenario": "客户说：'价格有点高，不过工艺确实不错'",
            "traditional": "关键词匹配：检测到'价格高' → 满意度下降",
            "intelligent": "智能分析：客户认可产品价值，只是价格敏感 → 满意度保持，购买意向需要价格调整"
        },
        {
            "scenario": "对话第1轮，客户说：'我买了！'",
            "traditional": "关键词匹配：检测到'买了' → 购买意向立即升至最高",
            "intelligent": "智能分析：考虑对话轮数，可能是误解或冲动，保持相对中立"
        }
    ]
    
    for i, case in enumerate(comparison_cases, 1):
        print(f"\n案例 {i}:")
        print(f"场景: {case['scenario']}")
        print(f"传统方法: {case['traditional']}")
        print(f"智能分析: {case['intelligent']}")
        print("-" * 40)

def main():
    """主函数"""
    try:
        # 运行智能分析演示
        success = demo_intelligent_analysis()
        
        if success:
            # 显示对比分析
            show_analysis_comparison()
            
            print("\n🎯 总结:")
            print("智能购买意向和满意度分析系统已成功实现！")
            print("系统能够：")
            print("1. 使用LLM理解客户真实意图")
            print("2. 在对话前期保持中立态度")
            print("3. 根据客户类型和对话内容动态调整")
            print("4. 提供详细的分析原因和关键信号")
            print("5. 在LLM失败时使用规则基础的后备方案")
        
        return success
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
