"""
LangGraph销售工作流实现
"""
from typing import Dict, Any, List
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict, Annotated
from agents.customer_agents import CustomerAgent, create_customer_agent
from agents.sales_agent import SalesAgent
from database.product_db import ProductDatabase
from langchain.llms.base import LLM
import config

class SalesState(TypedDict):
    """销售对话状态"""
    messages: Annotated[List[str], add_messages]
    customer_type: str
    customer_agent: CustomerAgent
    sales_agent: SalesAgent
    conversation_round: int
    sales_status: str  # "active", "success", "failed"
    customer_status: Dict
    sales_metrics: Dict

class SalesWorkflow:
    """销售工作流管理器"""
    
    def __init__(self, llm: LLM, db_path: str):
        self.llm = llm
        self.product_db = ProductDatabase(db_path)
        self.workflow = self._create_workflow()
    
    def _create_workflow(self) -> StateGraph:
        """创建LangGraph工作流"""
        workflow = StateGraph(SalesState)
        
        # 添加节点
        workflow.add_node("initialize", self._initialize_conversation)
        workflow.add_node("customer_response", self._customer_response)
        workflow.add_node("sales_response", self._sales_response)
        workflow.add_node("evaluate_status", self._evaluate_status)
        workflow.add_node("finalize", self._finalize_conversation)
        
        # 设置入口点
        workflow.set_entry_point("initialize")
        
        # 添加边
        workflow.add_edge("initialize", "customer_response")
        workflow.add_edge("customer_response", "sales_response")
        workflow.add_edge("sales_response", "evaluate_status")
        
        # 条件边：根据状态决定下一步
        workflow.add_conditional_edges(
            "evaluate_status",
            self._should_continue,
            {
                "continue": "customer_response",
                "success": "finalize",
                "failed": "finalize",
                "max_rounds": "finalize"
            }
        )
        
        workflow.add_edge("finalize", END)
        
        return workflow.compile()
    
    def _initialize_conversation(self, state: SalesState) -> SalesState:
        """初始化对话"""
        # 创建客户智能体
        customer_agent = create_customer_agent(state["customer_type"], self.llm)
        
        # 创建销售智能体
        sales_agent = SalesAgent(self.llm, self.product_db)
        
        # 销售员开场白
        sales_greeting = sales_agent.get_greeting()
        
        # 客户初始回应
        customer_greeting = customer_agent.get_initial_message()
        
        state.update({
            "customer_agent": customer_agent,
            "sales_agent": sales_agent,
            "messages": [f"销售员: {sales_greeting}", f"客户: {customer_greeting}"],
            "conversation_round": 1,
            "sales_status": "active",
            "customer_status": customer_agent.get_status(),
            "sales_metrics": sales_agent.get_sales_status()
        })
        
        return state
    
    def _customer_response(self, state: SalesState) -> SalesState:
        """客户回应节点"""
        if len(state["messages"]) < 2:
            return state
        
        # 获取最后一条销售员消息
        last_sales_message = state["messages"][-1]
        if last_sales_message.startswith("销售员: "):
            sales_content = last_sales_message[4:]  # 移除"销售员: "前缀
            
            # 客户回应
            customer_response = state["customer_agent"].respond_to_sales(sales_content)
            state["messages"].append(f"客户: {customer_response}")
            
            # 更新客户状态
            state["customer_status"] = state["customer_agent"].get_status()
        
        return state
    
    def _sales_response(self, state: SalesState) -> SalesState:
        """销售员回应节点"""
        if len(state["messages"]) < 2:
            return state
        
        # 获取最后一条客户消息
        last_customer_message = state["messages"][-1]
        if last_customer_message.startswith("客户: "):
            customer_content = last_customer_message[3:]  # 移除"客户: "前缀
            
            # 销售员回应
            sales_response = state["sales_agent"].respond_to_customer(
                customer_content, 
                state["customer_type"]
            )
            state["messages"].append(f"销售员: {sales_response}")
            
            # 更新销售状态
            state["sales_metrics"] = state["sales_agent"].get_sales_status()
            state["conversation_round"] += 1
        
        return state
    
    def _evaluate_status(self, state: SalesState) -> SalesState:
        """评估对话状态"""
        customer_agent = state["customer_agent"]
        
        # 检查成功条件
        if customer_agent.is_ready_to_buy():
            # 检查最后的客户消息是否包含购买意向
            last_message = state["messages"][-2] if len(state["messages"]) >= 2 else ""
            if any(keyword in last_message for keyword in config.SUCCESS_KEYWORDS):
                state["sales_status"] = "success"
                return state
        
        # 检查失败条件
        if customer_agent.is_lost():
            state["sales_status"] = "failed"
            return state
        
        # 检查最大轮数
        if state["conversation_round"] >= config.MAX_CONVERSATION_ROUNDS:
            state["sales_status"] = "max_rounds"
            return state
        
        # 继续对话
        state["sales_status"] = "active"
        return state
    
    def _should_continue(self, state: SalesState) -> str:
        """决定是否继续对话"""
        return state["sales_status"]
    
    def _finalize_conversation(self, state: SalesState) -> SalesState:
        """结束对话"""
        # 添加结束消息
        if state["sales_status"] == "success":
            final_message = "销售员: 太好了！感谢您的购买，我们会为您安排最好的服务。"
        elif state["sales_status"] == "failed":
            final_message = "销售员: 没关系，如果您以后有需要，随时欢迎您再来看看。"
        else:
            final_message = "销售员: 今天的时间差不多了，如果您还有疑问，欢迎随时联系我们。"
        
        state["messages"].append(final_message)
        return state
    
    def run_simulation(self, customer_type: str) -> Dict[str, Any]:
        """运行销售模拟"""
        initial_state = {
            "messages": [],
            "customer_type": customer_type,
            "customer_agent": None,
            "sales_agent": None,
            "conversation_round": 0,
            "sales_status": "active",
            "customer_status": {},
            "sales_metrics": {}
        }
        
        # 运行工作流
        final_state = self.workflow.invoke(initial_state)
        
        # 返回结果
        return {
            "customer_type": customer_type,
            "conversation": final_state["messages"],
            "final_status": final_state["sales_status"],
            "conversation_rounds": final_state["conversation_round"],
            "customer_final_status": final_state["customer_status"],
            "sales_final_metrics": final_state["sales_metrics"],
            "success": final_state["sales_status"] == "success"
        }
