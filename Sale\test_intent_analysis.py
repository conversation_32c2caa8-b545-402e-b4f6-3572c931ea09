"""
测试智能购买意向和满意度分析功能
"""
import sys

def test_intent_analyzer():
    """测试智能分析器"""
    print("🧠 测试智能购买意向和满意度分析器...")
    
    try:
        # 创建模拟LLM
        class MockLLM:
            def invoke(self, prompt):
                # 模拟LLM返回分析结果
                if "购买意向" in prompt:
                    if "我买了" in prompt or "成交" in prompt:
                        return MockResponse("0.9")
                    elif "太贵" in prompt or "不要" in prompt:
                        return MockResponse("0.2")
                    elif "考虑" in prompt or "不错" in prompt:
                        return MockResponse("0.6")
                    else:
                        return MockResponse("0.5")
                elif "满意度" in prompt:
                    if "很好" in prompt or "专业" in prompt:
                        return MockResponse("0.8")
                    elif "不满" in prompt or "糟糕" in prompt:
                        return MockResponse("0.2")
                    elif "可以" in prompt or "明白" in prompt:
                        return MockResponse("0.6")
                    else:
                        return MockResponse("0.5")
                else:
                    return MockResponse("0.5")
        
        class MockResponse:
            def __init__(self, content):
                self.content = content
        
        from utils.intent_analyzer import IntentAnalyzer
        
        # 创建分析器
        mock_llm = MockLLM()
        analyzer = IntentAnalyzer(mock_llm)
        
        print("✅ 智能分析器创建成功")
        
        # 测试场景
        test_scenarios = [
            {
                "name": "对话前期 - 中立态度",
                "customer_type": "预算敏感性",
                "conversation_round": 1,
                "current_intent": 0.5,
                "current_satisfaction": 0.5,
                "sales_message": "欢迎光临！我们有很多款式的黄金手镯。",
                "customer_response": "你好，我想看看。",
                "expected_range": (0.4, 0.6)
            },
            {
                "name": "积极反应 - 购买意向上升",
                "customer_type": "追求独特设计型",
                "conversation_round": 3,
                "current_intent": 0.5,
                "current_satisfaction": 0.6,
                "sales_message": "这款花丝工艺手镯设计独特，工艺精湛。",
                "customer_response": "这个设计很不错，我很喜欢。",
                "expected_range": (0.6, 0.8)
            },
            {
                "name": "强烈购买意向",
                "customer_type": "犹豫不决型",
                "conversation_round": 5,
                "current_intent": 0.7,
                "current_satisfaction": 0.7,
                "sales_message": "这款确实很适合您，我们提供完善的售后保障。",
                "customer_response": "好的，我买了！",
                "expected_range": (0.8, 1.0)
            },
            {
                "name": "消极反应 - 意向下降",
                "customer_type": "预算敏感性",
                "conversation_round": 4,
                "current_intent": 0.6,
                "current_satisfaction": 0.5,
                "sales_message": "这款手镯价格是3999元。",
                "customer_response": "太贵了，我不要了。",
                "expected_range": (0.1, 0.3)
            }
        ]
        
        # 运行测试场景
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📋 测试场景 {i}: {scenario['name']}")
            print("-" * 40)
            
            new_intent, new_satisfaction = analyzer.analyze_intent_and_satisfaction(
                customer_type=scenario["customer_type"],
                conversation_round=scenario["conversation_round"],
                current_intent=scenario["current_intent"],
                current_satisfaction=scenario["current_satisfaction"],
                sales_message=scenario["sales_message"],
                customer_response=scenario["customer_response"],
                conversation_history=[]
            )
            
            print(f"客户类型: {scenario['customer_type']}")
            print(f"对话轮数: {scenario['conversation_round']}")
            print(f"销售员: {scenario['sales_message']}")
            print(f"客户: {scenario['customer_response']}")
            print(f"购买意向: {scenario['current_intent']} → {new_intent:.2f}")
            print(f"满意度: {scenario['current_satisfaction']} → {new_satisfaction:.2f}")
            
            # 验证结果是否在预期范围内
            expected_min, expected_max = scenario["expected_range"]
            if expected_min <= new_intent <= expected_max:
                print(f"✅ 购买意向分析正确 (期望: {expected_min}-{expected_max})")
            else:
                print(f"⚠️ 购买意向可能需要调整 (期望: {expected_min}-{expected_max})")
        
        return True
        
    except Exception as e:
        print(f"❌ 智能分析器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customer_agent_with_llm_analysis():
    """测试客户智能体的LLM分析功能"""
    print("\n👤 测试客户智能体的智能分析功能...")
    
    try:
        # 创建模拟LLM
        class MockLLM:
            def invoke(self, prompt):
                if "购买意向" in prompt or "满意度" in prompt:
                    return MockResponse("0.6")
                else:
                    # 客户回应
                    if "预算敏感" in prompt:
                        return MockResponse("这个价格怎么样？有没有更便宜的？")
                    elif "独特设计" in prompt:
                        return MockResponse("这个设计很有特色，工艺如何？")
                    else:
                        return MockResponse("你觉得哪款更适合我？")
        
        class MockResponse:
            def __init__(self, content):
                self.content = content
        
        from agents.customer_agents import create_customer_agent
        
        # 测试不同客户类型
        customer_types = ["预算敏感性", "追求独特设计型", "犹豫不决型"]
        
        for customer_type in customer_types:
            print(f"\n🎭 测试 {customer_type} 客户...")
            
            mock_llm = MockLLM()
            customer = create_customer_agent(customer_type, mock_llm)
            
            # 初始状态检查
            initial_status = customer.get_status()
            print(f"初始购买意向: {initial_status['purchase_intent']}")
            print(f"初始满意度: {initial_status['satisfaction']}")
            print(f"分析方法: {initial_status['analysis_method']}")
            
            # 模拟销售对话
            sales_message = "欢迎光临！我们有各种款式的黄金手镯，请问您有什么特别的需求吗？"
            customer_response = customer.respond_to_sales(sales_message)
            
            # 检查更新后的状态
            updated_status = customer.get_status()
            print(f"客户回应: {customer_response}")
            print(f"更新后购买意向: {updated_status['purchase_intent']}")
            print(f"更新后满意度: {updated_status['satisfaction']}")
            print(f"对话轮数: {updated_status['conversation_rounds']}")
            
            # 验证对话前期保持中立
            if updated_status['conversation_rounds'] <= 2:
                if 0.4 <= updated_status['purchase_intent'] <= 0.6:
                    print("✅ 对话前期购买意向保持中立")
                else:
                    print("⚠️ 对话前期购买意向可能过于极端")
                
                if 0.4 <= updated_status['satisfaction'] <= 0.6:
                    print("✅ 对话前期满意度保持中立")
                else:
                    print("⚠️ 对话前期满意度可能过于极端")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户智能体测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧠 智能购买意向和满意度分析测试")
    print("=" * 60)
    
    tests = [
        ("智能分析器", test_intent_analyzer),
        ("客户智能体LLM分析", test_customer_agent_with_llm_analysis)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("=" * 40)
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 智能分析功能测试通过！")
        print("\n💡 新功能特点:")
        print("✅ 使用LLM智能判断购买意向和满意度")
        print("✅ 对话前期保持中立态度")
        print("✅ 根据客户类型和对话内容动态调整")
        print("✅ 提供规则基础的后备方案")
        print("✅ 支持详细的分析原因和关键信号")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
