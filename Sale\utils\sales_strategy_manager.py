"""
智能销售策略管理器 - 根据客户状态动态调整销售策略
"""
import json
import re
from typing import Dict, Tuple, Optional
from prompts.adaptive_sales_prompts import (
    STRATEGY_SELECTION_PROMPT,
    ADAPTIVE_STRATEGIES
)

class SalesStrategyManager:
    """智能销售策略管理器"""
    
    def __init__(self, llm):
        self.llm = llm
        self.strategy_history = []  # 策略使用历史
        
    def select_optimal_strategy(
        self,
        customer_type: str,
        purchase_intent: float,
        satisfaction: float,
        conversation_round: int,
        customer_message: str,
        conversation_history: list
    ) -> Dict:
        """
        选择最优销售策略
        
        Returns:
            Dict: 包含策略信息的字典
        """
        
        # 首先使用规则基础的快速判断
        quick_strategy = self._quick_strategy_selection(
            purchase_intent, satisfaction, customer_message
        )
        
        # 如果是紧急情况，直接返回
        if quick_strategy["urgency"] >= 4:
            return quick_strategy
        
        # 否则使用LLM进行详细分析
        try:
            llm_strategy = self._llm_strategy_selection(
                customer_type, purchase_intent, satisfaction,
                conversation_round, customer_message, conversation_history
            )
            
            # 合并结果
            strategy = {**quick_strategy, **llm_strategy}
            
        except Exception as e:
            print(f"LLM策略选择失败，使用规则基础策略: {e}")
            strategy = quick_strategy
        
        # 记录策略历史
        self.strategy_history.append({
            "round": conversation_round,
            "strategy": strategy["strategy"],
            "intent": purchase_intent,
            "satisfaction": satisfaction
        })
        
        return strategy
    
    def _quick_strategy_selection(
        self,
        purchase_intent: float,
        satisfaction: float,
        customer_message: str
    ) -> Dict:
        """快速策略选择（基于规则）"""
        
        # 检测紧急信号
        emergency_signals = ["再见", "走了", "不买", "不要", "离开", "算了", "浪费时间"]
        if any(signal in customer_message for signal in emergency_signals):
            return {
                "strategy": "紧急挽救",
                "priority": "挽救客户",
                "approach": "最后机会",
                "urgency": 5
            }
        
        # 基于购买意向和满意度的策略矩阵
        if purchase_intent >= 0.7 and satisfaction >= 0.6:
            return {
                "strategy": "积极成交",
                "priority": "立即成交",
                "approach": "抓住时机",
                "urgency": 4
            }
        elif purchase_intent >= 0.7 and satisfaction < 0.5:
            return {
                "strategy": "满意度提升",
                "priority": "修复关系",
                "approach": "重建信任",
                "urgency": 3
            }
        elif purchase_intent < 0.4 and satisfaction >= 0.6:
            return {
                "strategy": "欲望激发",
                "priority": "激发需求",
                "approach": "展示价值",
                "urgency": 2
            }
        elif purchase_intent < 0.4 and satisfaction < 0.5:
            return {
                "strategy": "客户挽救",
                "priority": "挽救客户",
                "approach": "重新开始",
                "urgency": 3
            }
        else:
            return {
                "strategy": "渐进推进",
                "priority": "稳步推进",
                "approach": "继续了解",
                "urgency": 2
            }
    
    def _llm_strategy_selection(
        self,
        customer_type: str,
        purchase_intent: float,
        satisfaction: float,
        conversation_round: int,
        customer_message: str,
        conversation_history: list
    ) -> Dict:
        """使用LLM进行策略选择"""
        
        history_str = "\n".join(conversation_history[-6:]) if conversation_history else "无"
        
        prompt = STRATEGY_SELECTION_PROMPT.format(
            customer_type=customer_type,
            purchase_intent=purchase_intent,
            satisfaction=satisfaction,
            conversation_round=conversation_round,
            customer_message=customer_message,
            conversation_history=history_str
        )
        
        response = self.llm.invoke(prompt)
        
        # 处理响应
        if hasattr(response, 'content'):
            response_text = response.content
        else:
            response_text = str(response)
        
        # 尝试解析JSON
        try:
            strategy_data = json.loads(response_text)
            return strategy_data
        except json.JSONDecodeError:
            # 如果JSON解析失败，尝试提取关键信息
            return self._extract_strategy_from_text(response_text)
    
    def _extract_strategy_from_text(self, text: str) -> Dict:
        """从文本中提取策略信息"""
        strategies = ["积极成交", "满意度提升", "欲望激发", "客户挽救", "渐进推进", "紧急挽救"]
        
        for strategy in strategies:
            if strategy in text:
                return {
                    "strategy": strategy,
                    "priority": "提取自文本",
                    "approach": "基于LLM分析",
                    "urgency": 2
                }
        
        return {
            "strategy": "渐进推进",
            "priority": "默认策略",
            "approach": "稳步推进",
            "urgency": 2
        }
    
    def generate_adaptive_response(
        self,
        strategy: Dict,
        customer_type: str,
        purchase_intent: float,
        satisfaction: float,
        customer_message: str,
        conversation_history: list,
        recommended_products: list = None,
        current_stage: str = "探索阶段"
    ) -> str:
        """
        根据策略生成自适应回应
        """
        
        strategy_name = strategy["strategy"]
        
        if strategy_name not in ADAPTIVE_STRATEGIES:
            strategy_name = "渐进推进"  # 默认策略
        
        prompt_template = ADAPTIVE_STRATEGIES[strategy_name]
        history_str = "\n".join(conversation_history[-6:]) if conversation_history else "无"
        
        # 根据不同策略填充不同的参数
        if strategy_name == "积极成交":
            prompt = prompt_template.format(
                purchase_intent=purchase_intent,
                satisfaction=satisfaction,
                customer_type=customer_type,
                customer_message=customer_message,
                conversation_history=history_str,
                recommended_products=self._format_products(recommended_products)
            )
        elif strategy_name in ["满意度提升", "客户挽救", "紧急挽救"]:
            prompt = prompt_template.format(
                purchase_intent=purchase_intent,
                satisfaction=satisfaction,
                customer_type=customer_type,
                customer_message=customer_message,
                conversation_history=history_str
            )
        elif strategy_name == "欲望激发":
            prompt = prompt_template.format(
                purchase_intent=purchase_intent,
                satisfaction=satisfaction,
                customer_type=customer_type,
                customer_message=customer_message,
                conversation_history=history_str,
                recommended_products=self._format_products(recommended_products)
            )
        else:  # 渐进推进
            prompt = prompt_template.format(
                purchase_intent=purchase_intent,
                satisfaction=satisfaction,
                customer_type=customer_type,
                customer_message=customer_message,
                conversation_history=history_str,
                current_stage=current_stage
            )
        
        # 生成回应
        response = self.llm.invoke(prompt)
        
        if hasattr(response, 'content'):
            return response.content.replace('"', '')
        else:
            return str(response).replace('"', '')
    
    def _format_products(self, products) -> str:
        """格式化产品信息"""
        if not products:
            return "当前推荐的产品"
        
        if isinstance(products, list) and len(products) > 0:
            if hasattr(products[0], 'name'):
                # Product对象
                return ", ".join([f"{p.name}({p.price}元)" for p in products[:2]])
            else:
                # 字符串列表
                return ", ".join(products[:2])
        
        return str(products)
    
    def get_strategy_analysis(self) -> Dict:
        """获取策略使用分析"""
        if not self.strategy_history:
            return {"total_strategies": 0}
        
        strategy_counts = {}
        for record in self.strategy_history:
            strategy = record["strategy"]
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
        
        return {
            "total_strategies": len(self.strategy_history),
            "strategy_distribution": strategy_counts,
            "latest_strategy": self.strategy_history[-1]["strategy"],
            "strategy_progression": [r["strategy"] for r in self.strategy_history]
        }
    
    def should_escalate_urgency(
        self,
        current_strategy: Dict,
        conversation_round: int,
        purchase_intent: float,
        satisfaction: float
    ) -> bool:
        """判断是否需要提升紧急程度"""
        
        # 如果对话轮数过多但没有进展
        if conversation_round >= 8 and purchase_intent < 0.5:
            return True
        
        # 如果满意度持续下降
        if len(self.strategy_history) >= 2:
            recent_records = self.strategy_history[-2:]
            if all(r["satisfaction"] < 0.4 for r in recent_records):
                return True
        
        # 如果当前策略紧急程度较低但客户状态不佳
        if current_strategy["urgency"] <= 2 and purchase_intent < 0.3 and satisfaction < 0.4:
            return True
        
        return False
