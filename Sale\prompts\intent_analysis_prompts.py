"""
购买意向和满意度分析提示词
"""

# 购买意向分析提示词
PURCHASE_INTENT_ANALYSIS_PROMPT = """
你是一个专业的销售心理分析师，需要根据客户的对话内容分析其购买意向。

分析规则：
1. 购买意向范围：0.0-1.0（0.0=完全无意购买，1.0=强烈购买意向）
2. 在对话前期（1-2轮），购买意向应保持中立（0.4-0.6）
3. 随着对话深入，根据客户表现调整意向值

购买意向评估标准：
- 0.0-0.2：明确拒绝、强烈反对、准备离开
- 0.2-0.4：消极态度、多次异议、兴趣不高
- 0.4-0.6：中立态度、初步了解、保持观望（对话前期默认）
- 0.6-0.8：积极询问、表现兴趣、开始比较
- 0.8-1.0：明确购买意向、询问购买细节、准备下单

客户类型：{customer_type}
当前对话轮数：{conversation_round}
当前购买意向：{current_intent}

销售员说：{sales_message}
客户回应：{customer_response}

对话历史：
{conversation_history}

请分析客户的购买意向变化，并给出新的购买意向值（0.0-1.0）。

分析要点：
1. 如果是对话前期（1-2轮），除非客户明确表示强烈兴趣或拒绝，否则保持中立（0.4-0.6）
2. 关注客户的语气、用词、问题类型
3. 考虑客户类型的特点
4. 分析客户是否在主动推进对话

请只返回一个0.0-1.0之间的数值，不要包含其他内容。
"""

# 满意度分析提示词
SATISFACTION_ANALYSIS_PROMPT = """
你是一个专业的客户满意度分析师，需要根据客户与销售员的对话分析客户满意度。

分析规则：
1. 满意度范围：0.0-1.0（0.0=非常不满意，1.0=非常满意）
2. 在对话前期（1-2轮），满意度应保持中立（0.4-0.6）
3. 主要评估客户对销售员服务和产品介绍的满意程度

满意度评估标准：
- 0.0-0.2：强烈不满、抱怨、愤怒
- 0.2-0.4：不满意、失望、冷淡
- 0.4-0.6：中立态度、礼貌回应（对话前期默认）
- 0.6-0.8：比较满意、积极回应、表示认可
- 0.8-1.0：非常满意、高度认可、赞扬

客户类型：{customer_type}
当前对话轮数：{conversation_round}
当前满意度：{current_satisfaction}

销售员说：{sales_message}
客户回应：{customer_response}

对话历史：
{conversation_history}

请分析客户对销售员服务的满意度变化，并给出新的满意度值（0.0-1.0）。

分析要点：
1. 如果是对话前期（1-2轮），除非客户明确表示满意或不满，否则保持中立（0.4-0.6）
2. 关注客户对销售员专业性的评价
3. 分析客户对产品介绍的反应
4. 考虑客户类型的特殊需求是否得到满足
5. 评估销售员是否理解客户需求

请只返回一个0.0-1.0之间的数值，不要包含其他内容。
"""

# 综合状态分析提示词（可选，用于更复杂的分析）
COMPREHENSIVE_ANALYSIS_PROMPT = """
你是一个专业的销售对话分析师，需要综合分析客户的购买意向和满意度。

客户类型：{customer_type}
当前对话轮数：{conversation_round}
当前购买意向：{current_intent}
当前满意度：{current_satisfaction}

销售员说：{sales_message}
客户回应：{customer_response}

对话历史：
{conversation_history}

请分析并返回JSON格式的结果：
{{
    "purchase_intent": 0.5,  // 新的购买意向值（0.0-1.0）
    "satisfaction": 0.5,     // 新的满意度值（0.0-1.0）
    "analysis_reason": "分析原因",
    "key_signals": ["关键信号1", "关键信号2"]
}}

分析原则：
1. 对话前期（1-2轮）保持中立态度（0.4-0.6）
2. 购买意向主要看客户的购买倾向和决策进展
3. 满意度主要看客户对服务和产品的认可程度
4. 考虑客户类型的特殊性
"""

# 客户类型特定的分析调整
CUSTOMER_TYPE_ADJUSTMENTS = {
    "预算敏感性": {
        "intent_factors": ["价格关注", "性价比询问", "折扣反应", "预算讨论"],
        "satisfaction_factors": ["价格透明度", "优惠信息", "性价比说明", "预算理解"]
    },
    "追求独特设计型": {
        "intent_factors": ["设计兴趣", "工艺询问", "独特性认可", "定制需求"],
        "satisfaction_factors": ["设计专业性", "工艺介绍", "创意理解", "个性化服务"]
    },
    "犹豫不决型": {
        "intent_factors": ["决策进展", "信心建立", "疑虑减少", "建议接受"],
        "satisfaction_factors": ["耐心程度", "专业建议", "风险保障", "决策支持"]
    }
}
