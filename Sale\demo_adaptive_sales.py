"""
智能自适应销售策略演示
展示销售智能体如何根据客户状态动态调整策略以促成交易
"""
import sys

def create_enhanced_adaptive_llm():
    """创建增强的自适应模拟LLM"""
    
    class EnhancedAdaptiveLLM:
        def __init__(self):
            self.conversation_count = 0
            self.strategy_calls = []
            
        def invoke(self, prompt):
            self.conversation_count += 1
            
            # 策略选择（JSON格式）
            if "策略分析师" in prompt and "策略选择" in prompt:
                if "购买意向：0.8" in prompt and "满意度：0.7" in prompt:
                    strategy = '{"strategy": "积极成交", "priority": "立即成交", "approach": "抓住时机", "urgency": 4}'
                elif "购买意向：0.8" in prompt and "满意度：0.3" in prompt:
                    strategy = '{"strategy": "满意度提升", "priority": "修复关系", "approach": "重建信任", "urgency": 3}'
                elif "购买意向：0.2" in prompt and "满意度：0.7" in prompt:
                    strategy = '{"strategy": "欲望激发", "priority": "激发需求", "approach": "展示价值", "urgency": 2}'
                elif "购买意向：0.2" in prompt and "满意度：0.2" in prompt:
                    strategy = '{"strategy": "客户挽救", "priority": "挽救客户", "approach": "重新开始", "urgency": 3}'
                elif "再见" in prompt or "离开" in prompt:
                    strategy = '{"strategy": "紧急挽救", "priority": "最后机会", "approach": "挽留客户", "urgency": 5}'
                else:
                    strategy = '{"strategy": "渐进推进", "priority": "稳步推进", "approach": "继续了解", "urgency": 2}'
                
                self.strategy_calls.append(strategy)
                return MockResponse(strategy)
            
            # 自适应销售话术
            elif "积极成交" in prompt:
                return MockResponse("看得出您很喜欢这款手镯！这确实是我们的精品，今天购买还有特别优惠。我现在就为您办理购买手续，您觉得怎么样？")
            elif "满意度提升" in prompt:
                return MockResponse("我注意到您可能有些担心，请告诉我具体的顾虑。作为专业顾问，我向您保证我们的产品质量和售后服务都是一流的。")
            elif "欲望激发" in prompt:
                return MockResponse("想象一下佩戴这款手镯时的优雅气质！这种传统工艺现在很少见了，而且黄金手镯还有很好的保值升值空间。")
            elif "客户挽救" in prompt:
                return MockResponse("很抱歉让您有不好的体验。让我重新为您介绍，我相信我们能找到最适合您的产品。给我一个机会重新证明我们的专业性。")
            elif "紧急挽救" in prompt:
                return MockResponse("请等一下！作为今天的特殊优惠，我可以给您额外的折扣。这真的是很难得的机会，您再考虑一下好吗？")
            elif "渐进推进" in prompt:
                return MockResponse("让我更详细地了解您的需求。根据您刚才提到的，我推荐这几款产品，它们都很符合您的要求。")
            
            # 购买意向分析
            elif "购买意向" in prompt:
                if "我买了" in prompt or "成交" in prompt:
                    return MockResponse("0.9")
                elif "很喜欢" in prompt or "不错" in prompt:
                    return MockResponse("0.7")
                elif "考虑" in prompt:
                    return MockResponse("0.6")
                elif "太贵" in prompt or "不要" in prompt:
                    return MockResponse("0.2")
                elif "再见" in prompt or "离开" in prompt:
                    return MockResponse("0.1")
                else:
                    return MockResponse("0.5")
            
            # 满意度分析
            elif "满意度" in prompt:
                if "专业" in prompt or "很好" in prompt:
                    return MockResponse("0.8")
                elif "满意" in prompt or "不错" in prompt:
                    return MockResponse("0.7")
                elif "可以" in prompt:
                    return MockResponse("0.6")
                elif "不满" in prompt or "问题" in prompt:
                    return MockResponse("0.3")
                elif "糟糕" in prompt or "差" in prompt:
                    return MockResponse("0.2")
                else:
                    return MockResponse("0.5")
            
            # 客户回应（根据客户类型）
            else:
                if "预算敏感" in prompt:
                    responses = [
                        "你好，我想看看黄金手镯，有什么性价比高的推荐吗？",
                        "这个价格怎么样？有没有更便宜一点的？",
                        "看起来不错，但价格有点超预算了。",
                        "如果有优惠的话我可以考虑。",
                        "好的，我买了！"
                    ]
                elif "独特设计" in prompt:
                    responses = [
                        "你好，我在找一些设计独特的黄金手镯。",
                        "这个设计很有特色，工艺怎么样？",
                        "我很喜欢这种传统工艺的感觉。",
                        "这款确实很特别，我很满意。",
                        "就要这款了！"
                    ]
                else:  # 犹豫不决型
                    responses = [
                        "你好，我想买个黄金手镯，但不太确定选哪款。",
                        "你觉得哪款更适合我？我有点选择困难。",
                        "这个建议很专业，让我更有信心了。",
                        "你推荐得很好，我相信你的判断。",
                        "好吧，我相信你的推荐，就要这个。"
                    ]
                
                index = min(self.conversation_count // 2, len(responses) - 1)
                return MockResponse(responses[index])
    
    class MockResponse:
        def __init__(self, content):
            self.content = content
    
    return EnhancedAdaptiveLLM()

def demo_adaptive_sales_strategies():
    """演示智能自适应销售策略"""
    print("🎯 智能自适应销售策略演示")
    print("=" * 60)
    print("展示销售智能体如何根据客户状态动态调整策略以促成交易")
    print("=" * 60)
    
    try:
        from simple_workflow import SimpleSalesWorkflow
        from utils.conversation_manager import ConversationManager
        
        # 创建增强的自适应LLM
        adaptive_llm = create_enhanced_adaptive_llm()
        
        # 初始化组件
        workflow = SimpleSalesWorkflow(adaptive_llm, "demo_adaptive_sales.db")
        conversation_manager = ConversationManager("adaptive_sales_logs.json")
        
        # 客户类型和对应的演示场景
        demo_scenarios = [
            {
                "customer_type": "预算敏感性",
                "description": "价格敏感客户 - 展示如何通过价值重塑和优惠策略促成交易",
                "key_strategies": ["需求探寻", "价值展示", "优惠提供", "积极成交"]
            },
            {
                "customer_type": "追求独特设计型", 
                "description": "设计追求客户 - 展示如何通过工艺介绍和价值激发促成交易",
                "key_strategies": ["设计展示", "工艺介绍", "价值激发", "积极成交"]
            },
            {
                "customer_type": "犹豫不决型",
                "description": "犹豫不决客户 - 展示如何通过专业建议和信心建立促成交易",
                "key_strategies": ["专业建议", "信心建立", "风险消除", "引导成交"]
            }
        ]
        
        print("\n🎭 开始演示三种客户类型的自适应销售策略...\n")
        
        for i, scenario in enumerate(demo_scenarios, 1):
            print(f"\n{'='*20} 场景 {i}: {scenario['description']} {'='*20}")
            
            # 重置LLM状态
            adaptive_llm.conversation_count = 0
            adaptive_llm.strategy_calls = []
            
            try:
                # 运行销售模拟
                result = workflow.run_simulation(scenario["customer_type"])
                
                # 显示对话过程
                print(f"\n💬 销售对话过程:")
                print("-" * 50)
                for j, message in enumerate(result["conversation"], 1):
                    if message.startswith("销售员: "):
                        print(f"{j}. 🛍️  {message}")
                    elif message.startswith("客户: "):
                        print(f"{j}. 👤 {message}")
                    else:
                        print(f"{j}.    {message}")
                
                # 分析销售策略使用情况
                print(f"\n📊 智能策略分析:")
                print("-" * 50)
                
                customer_status = result["customer_final_status"]
                sales_metrics = result["sales_final_metrics"]
                
                print(f"客户类型: {customer_status['customer_type']}")
                print(f"最终购买意向: {customer_status['purchase_intent']:.2f}")
                print(f"最终满意度: {customer_status['satisfaction']:.2f}")
                print(f"对话轮数: {customer_status['conversation_rounds']}")
                
                # 显示策略使用历史
                if "strategy_analysis" in sales_metrics:
                    strategy_analysis = sales_metrics["strategy_analysis"]
                    if strategy_analysis.get("total_strategies", 0) > 0:
                        print(f"使用策略数: {strategy_analysis['total_strategies']}")
                        print(f"策略分布: {strategy_analysis.get('strategy_distribution', {})}")
                        print(f"最新策略: {strategy_analysis.get('latest_strategy', '未知')}")
                
                # 显示策略调用历史
                if adaptive_llm.strategy_calls:
                    print(f"策略调用历史: {len(adaptive_llm.strategy_calls)} 次")
                    for k, call in enumerate(adaptive_llm.strategy_calls, 1):
                        print(f"  {k}. {call}")
                
                # 分析结果
                print(f"\n🎯 销售结果分析:")
                print("-" * 50)
                
                success = result["success"]
                final_status = result["final_status"]
                
                if success:
                    print("✅ 交易成功！")
                    print("🏆 成功因素:")
                    if customer_status['purchase_intent'] >= 0.8:
                        print("  - 购买意向达到高水平")
                    if customer_status['satisfaction'] >= 0.7:
                        print("  - 客户满意度很高")
                    print("  - 智能策略调整发挥了关键作用")
                else:
                    print("❌ 交易未成功")
                    print("📋 改进建议:")
                    if customer_status['purchase_intent'] < 0.5:
                        print("  - 需要更好地激发购买欲望")
                    if customer_status['satisfaction'] < 0.5:
                        print("  - 需要提升客户满意度")
                    print("  - 可以尝试更积极的成交策略")
                
                # 保存结果
                conversation_manager.save_conversation(result)
                
            except Exception as e:
                print(f"❌ 场景 {i} 演示失败: {e}")
            
            if i < len(demo_scenarios):
                input(f"\n按回车键继续下一个场景...")
        
        print("\n" + "="*60)
        print("🎉 智能自适应销售策略演示完成！")
        
        # 显示整体统计
        conversation_manager.print_statistics()
        
        # 显示核心优势
        print("\n💡 智能自适应销售策略的核心优势:")
        print("✅ 实时状态感知 - 动态监测客户购买意向和满意度")
        print("✅ 策略智能选择 - 根据客户状态自动选择最优销售策略")
        print("✅ 话术自适应生成 - 针对不同策略生成相应的销售话术")
        print("✅ 成交导向优化 - 所有策略都以促成交易为最终目标")
        print("✅ 紧急情况处理 - 自动识别客户流失风险并采取挽救措施")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_strategy_matrix():
    """展示策略选择矩阵"""
    print("\n📊 智能销售策略选择矩阵")
    print("=" * 60)
    
    matrix = """
    购买意向 ↑
         │
    高   │  满意度提升    │    积极成交
         │  (修复关系)    │   (抓住时机)
         │               │
    ─────┼───────────────┼──────────────→ 满意度
         │               │
    低   │  客户挽救      │    欲望激发
         │  (重新开始)    │   (展示价值)
         │               │
         低              高
    
    策略说明:
    
    🎯 积极成交 (高意向+高满意度)
       - 立即抓住成交机会
       - 使用假设成交法
       - 提供购买便利和优惠
    
    🔧 满意度提升 (高意向+低满意度)  
       - 先解决客户不满
       - 重建信任关系
       - 然后推进成交
    
    💎 欲望激发 (低意向+高满意度)
       - 展示产品独特价值
       - 创造拥有后的美好场景
       - 强调稀缺性和机会成本
    
    🚨 客户挽救 (低意向+低满意度)
       - 重新开始销售流程
       - 提供特殊优惠补偿
       - 重建客户信心
    
    ⚡ 紧急挽救 (客户要离开)
       - 最后的挽留机会
       - 提供最大让步
       - 请求再次机会
    """
    
    print(matrix)

def main():
    """主函数"""
    try:
        # 运行自适应销售策略演示
        success = demo_adaptive_sales_strategies()
        
        if success:
            # 显示策略矩阵
            show_strategy_matrix()
            
            print("\n🎯 总结:")
            print("智能自适应销售策略系统已成功实现！")
            print("\n核心能力:")
            print("1. 🧠 智能状态分析 - 实时评估客户购买意向和满意度")
            print("2. 🎯 策略动态选择 - 根据客户状态自动选择最优策略")
            print("3. 💬 话术自适应生成 - 针对不同策略生成相应话术")
            print("4. 🚀 成交导向优化 - 所有策略都以促成交易为目标")
            print("5. 🔄 实时策略调整 - 根据对话进展动态调整策略")
            
            print("\n这个系统真正实现了'以成交为目标的智能销售'！")
        
        return success
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
