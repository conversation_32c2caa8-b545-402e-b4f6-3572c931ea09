"""
黄金手镯销售模拟主程序
"""
import os
import time

from workflows.sales_workflow import SalesWorkflow
from utils.conversation_manager import ConversationManager
import config
from langchain_openai import ChatOpenAI,OpenAI

# from dotenv import load_dotenv 


# load_dotenv()
#llm = OpenAI(model = config.MODEL_NAME, temperature=config.TEMPERATURE,max_tokens=config.MAX_TOKENS ,api_key=config.OPENAI_API_KEY,base_url=config.BASE_URL)
from langchain_deepseek import ChatDeepSeek

llm = ChatDeepSeek(
    model=config.MODEL_NAME,
    temperature=config.TEMPERATURE,
    max_tokens=config.MAX_TOKENS,
    api_key=config.DEEPSEEK_API_KEY,
    base_url=config.BASE_URL
    # other params...
)
# class DeepSeekLLM(OpenAI):
#     """DeepSeek LLM包装器"""
    
#     def __init__(self):
#         super().__init__(
#             openai_api_key=config.DEEPSEEK_API_KEY,
#             openai_api_base=config.DEEPSEEK_BASE_URL,
#             model_name=config.MODEL_NAME,
#             temperature=config.TEMPERATURE,
#             max_tokens=config.MAX_TOKENS
#         )

def main():
    """主函数"""
    print("🛍️ 黄金手镯销售模拟系统")
    print("=" * 50)
    choice = "1"
    
    # 检查API密钥
    if config.DEEPSEEK_API_KEY == "your-deepseek-api-key-here":
        print("⚠️  请在config.py中设置您的DeepSeek API密钥")
        return
    
    # 初始化组件
    try:
        # llm = DeepSeekLLM()
        workflow = SalesWorkflow(llm, config.DATABASE_PATH)
        conversation_manager = ConversationManager()
        
        print("✅ 系统初始化完成")
    except Exception as e:
        print(f"❌ 系统初始化失败: {e}")
        return
    
    # 客户类型选项
    customer_types = ["预算敏感性", "追求独特设计型", "犹豫不决型"]
    if choice == "1":
            # 单次模拟
            print("\n请选择客户类型:")
            for i, customer_type in enumerate(customer_types, 1):
                print(f"{i}. {customer_type}")
            
            try:
                type_choice = 0
                if 0 <= type_choice < len(customer_types):
                    customer_type = customer_types[type_choice]
                    run_single_simulation(workflow, conversation_manager, customer_type)
                else:
                    print("❌ 无效的选择")
            except ValueError:
                print("❌ 请输入有效的数字")
    
    # while True:
    #     print("\n" + "=" * 50)
    #     print("请选择操作:")
    #     print("1. 开始销售模拟")
    #     print("2. 查看统计报告")
    #     print("3. 查看最近对话")
    #     print("4. 批量模拟测试")
    #     print("0. 退出系统")
        
    #     choice = input("\n请输入选择 (0-4): ").strip()
        
    #     if choice == "0":
    #         print("👋 感谢使用销售模拟系统！")
    #         break
        
    #     elif choice == "1":
    #         # 单次模拟
    #         print("\n请选择客户类型:")
    #         for i, customer_type in enumerate(customer_types, 1):
    #             print(f"{i}. {customer_type}")
            
    #         try:
    #             type_choice = int(input("\n请输入客户类型 (1-3): ")) - 1
    #             if 0 <= type_choice < len(customer_types):
    #                 customer_type = customer_types[type_choice]
    #                 run_single_simulation(workflow, conversation_manager, customer_type)
    #             else:
    #                 print("❌ 无效的选择")
    #         except ValueError:
    #             print("❌ 请输入有效的数字")
        
    #     elif choice == "2":
    #         # 查看统计报告
    #         conversation_manager.print_statistics()
        
    #     elif choice == "3":
    #         # 查看最近对话
    #         recent_conversations = conversation_manager.get_recent_conversations(3)
    #         if recent_conversations:
    #             print(f"\n📋 最近 {len(recent_conversations)} 次对话:")
    #             for i, conv in enumerate(recent_conversations, 1):
    #                 print(f"\n{i}. {conv.customer_type} - {conv.final_status} - {conv.timestamp}")
    #                 conversation_manager.print_conversation(conv.conversation)
    #         else:
    #             print("\n📋 暂无对话记录")
        
    #     elif choice == "4":
    #         # 批量测试
    #         print("\n🔄 开始批量模拟测试...")
    #         run_batch_simulation(workflow, conversation_manager, customer_types)
        
    #     else:
    #         print("❌ 无效的选择，请重新输入")

def run_single_simulation(workflow: SalesWorkflow, conversation_manager: ConversationManager, customer_type: str):
    """运行单次模拟"""
    print(f"\n🎭 开始模拟 - 客户类型: {customer_type}")
    print("⏳ 模拟进行中...")
    
    start_time = time.time()
    
    try:
        # 运行模拟
        result = workflow.run_simulation(customer_type)
        
        end_time = time.time()
        duration = end_time - start_time
        
        # 保存结果
        conversation_manager.save_conversation(result, duration)
        
        # 显示结果
        print(f"\n✅ 模拟完成!")
        print(f"📊 结果: {'成功交易' if result['success'] else '未成交'}")
        print(f"🔄 对话轮数: {result['conversation_rounds']}")
        print(f"⏱️  耗时: {duration:.1f}秒")
        
        # 显示对话内容
        conversation_manager.print_conversation(result["conversation"])
        
        # 显示详细状态
        print("📈 客户最终状态:")
        for key, value in result["customer_final_status"].items():
            print(f"  {key}: {value}")
        
        print("\n📈 销售最终指标:")
        for key, value in result["sales_final_metrics"].items():
            print(f"  {key}: {value}")
    
    except Exception as e:
        print(f"❌ 模拟失败: {e}")

def run_batch_simulation(workflow: SalesWorkflow, conversation_manager: ConversationManager, customer_types: list):
    """运行批量模拟"""
    total_simulations = len(customer_types) * 2  # 每种类型运行2次
    
    print(f"🔄 将运行 {total_simulations} 次模拟...")
    
    for customer_type in customer_types:
        for i in range(2):
            print(f"\n进行第 {customer_types.index(customer_type) * 2 + i + 1}/{total_simulations} 次模拟")
            print(f"客户类型: {customer_type}")
            
            start_time = time.time()
            
            try:
                result = workflow.run_simulation(customer_type)
                duration = time.time() - start_time
                
                conversation_manager.save_conversation(result, duration)
                
                status = "✅ 成功" if result["success"] else "❌ 失败"
                print(f"结果: {status} (轮数: {result['conversation_rounds']}, 耗时: {duration:.1f}s)")
                
            except Exception as e:
                print(f"❌ 模拟失败: {e}")
    
    print(f"\n🎉 批量模拟完成！")
    conversation_manager.print_statistics()

if __name__ == "__main__":
    main()
