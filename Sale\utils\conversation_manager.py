"""
对话管理工具
"""
import json
import datetime
from typing import Dict, List, Any
from dataclasses import dataclass, asdict

@dataclass
class ConversationRecord:
    """对话记录数据类"""
    timestamp: str
    customer_type: str
    conversation: List[str]
    final_status: str
    conversation_rounds: int
    customer_final_status: Dict
    sales_final_metrics: Dict
    success: bool
    duration_seconds: float = 0.0

class ConversationManager:
    """对话管理器"""
    
    def __init__(self, log_file: str = "conversation_logs.json"):
        self.log_file = log_file
        self.conversations = []
        self.load_conversations()
    
    def save_conversation(self, simulation_result: Dict[str, Any], duration: float = 0.0):
        """保存对话记录"""
        record = ConversationRecord(
            timestamp=datetime.datetime.now().isoformat(),
            customer_type=simulation_result["customer_type"],
            conversation=simulation_result["conversation"],
            final_status=simulation_result["final_status"],
            conversation_rounds=simulation_result["conversation_rounds"],
            customer_final_status=simulation_result["customer_final_status"],
            sales_final_metrics=simulation_result["sales_final_metrics"],
            success=simulation_result["success"],
            duration_seconds=duration
        )
        
        self.conversations.append(record)
        self._save_to_file()
    
    def load_conversations(self):
        """从文件加载对话记录"""
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                self.conversations = [ConversationRecord(**item) for item in data]
        except FileNotFoundError:
            self.conversations = []
        except Exception as e:
            print(f"加载对话记录失败: {e}")
            self.conversations = []
    
    def _save_to_file(self):
        """保存到文件"""
        try:
            with open(self.log_file, 'w', encoding='utf-8') as f:
                data = [asdict(record) for record in self.conversations]
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"保存对话记录失败: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.conversations:
            return {"total_conversations": 0}
        
        total = len(self.conversations)
        successful = sum(1 for conv in self.conversations if conv.success)
        
        # 按客户类型统计
        by_customer_type = {}
        for conv in self.conversations:
            customer_type = conv.customer_type
            if customer_type not in by_customer_type:
                by_customer_type[customer_type] = {"total": 0, "success": 0}
            by_customer_type[customer_type]["total"] += 1
            if conv.success:
                by_customer_type[customer_type]["success"] += 1
        
        # 计算成功率
        for customer_type in by_customer_type:
            stats = by_customer_type[customer_type]
            stats["success_rate"] = stats["success"] / stats["total"] if stats["total"] > 0 else 0
        
        # 平均对话轮数
        avg_rounds = sum(conv.conversation_rounds for conv in self.conversations) / total
        
        # 平均对话时长
        avg_duration = sum(conv.duration_seconds for conv in self.conversations) / total
        
        return {
            "total_conversations": total,
            "successful_conversations": successful,
            "overall_success_rate": successful / total,
            "average_conversation_rounds": avg_rounds,
            "average_duration_seconds": avg_duration,
            "by_customer_type": by_customer_type
        }
    
    def print_conversation(self, conversation: List[str]):
        """打印对话内容"""
        print("\n" + "="*50)
        print("对话记录:")
        print("="*50)
        for message in conversation:
            if message.startswith("销售员: "):
                print(f"🛍️  {message}")
            elif message.startswith("客户: "):
                print(f"👤 {message}")
            else:
                print(f"   {message}")
        print("="*50 + "\n")
    
    def print_statistics(self):
        """打印统计信息"""
        stats = self.get_statistics()
        
        print("\n" + "="*60)
        print("📊 销售模拟统计报告")
        print("="*60)
        
        if stats["total_conversations"] == 0:
            print("暂无对话记录")
            return
        
        print(f"总对话次数: {stats['total_conversations']}")
        print(f"成功交易次数: {stats['successful_conversations']}")
        print(f"整体成功率: {stats['overall_success_rate']:.2%}")
        print(f"平均对话轮数: {stats['average_conversation_rounds']:.1f}")
        print(f"平均对话时长: {stats['average_duration_seconds']:.1f}秒")
        
        print("\n📈 按客户类型统计:")
        print("-" * 40)
        for customer_type, type_stats in stats["by_customer_type"].items():
            print(f"{customer_type}:")
            print(f"  总次数: {type_stats['total']}")
            print(f"  成功次数: {type_stats['success']}")
            print(f"  成功率: {type_stats['success_rate']:.2%}")
        
        print("="*60 + "\n")
    
    def get_recent_conversations(self, limit: int = 5) -> List[ConversationRecord]:
        """获取最近的对话记录"""
        return sorted(self.conversations, key=lambda x: x.timestamp, reverse=True)[:limit]
