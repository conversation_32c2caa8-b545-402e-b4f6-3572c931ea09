"""
测试智能自适应销售策略功能
"""
import sys

def create_adaptive_mock_llm():
    """创建支持自适应策略的模拟LLM"""
    
    class AdaptiveMockLLM:
        def __init__(self):
            self.call_count = 0
            
        def invoke(self, prompt):
            self.call_count += 1
            
            # 策略选择分析
            if "策略分析师" in prompt and "策略选择" in prompt:
                if "购买意向：0.8" in prompt and "满意度：0.7" in prompt:
                    return MockResponse('{"strategy": "积极成交", "priority": "立即成交", "approach": "抓住时机", "urgency": 4}')
                elif "购买意向：0.8" in prompt and "满意度：0.3" in prompt:
                    return MockResponse('{"strategy": "满意度提升", "priority": "修复关系", "approach": "重建信任", "urgency": 3}')
                elif "购买意向：0.2" in prompt and "满意度：0.7" in prompt:
                    return MockResponse('{"strategy": "欲望激发", "priority": "激发需求", "approach": "展示价值", "urgency": 2}')
                elif "购买意向：0.2" in prompt and "满意度：0.2" in prompt:
                    return MockResponse('{"strategy": "客户挽救", "priority": "挽救客户", "approach": "重新开始", "urgency": 3}')
                else:
                    return MockResponse('{"strategy": "渐进推进", "priority": "稳步推进", "approach": "继续了解", "urgency": 2}')
            
            # 自适应销售话术生成
            elif "积极成交" in prompt:
                return MockResponse("看得出您很喜欢这款，我们现在就办理购买手续吧！这款确实很适合您，今天购买还有特别优惠。")
            elif "满意度提升" in prompt:
                return MockResponse("我注意到您可能有些担心，请告诉我具体的顾虑。作为专业顾问，我向您保证我们的产品质量和服务。")
            elif "欲望激发" in prompt:
                return MockResponse("想象一下佩戴这款手镯时的优雅气质，这种工艺的手镯升值空间很大，现在是购买的最佳时机。")
            elif "客户挽救" in prompt:
                return MockResponse("很抱歉让您有不好的体验，让我重新为您服务。给我一个机会重新证明我们的专业性。")
            elif "渐进推进" in prompt:
                return MockResponse("让我更详细地了解您的需求，基于您刚才提到的，我推荐这几款产品。")
            
            # 购买意向和满意度分析
            elif "购买意向" in prompt:
                if "我买了" in prompt:
                    return MockResponse("0.9")
                elif "考虑" in prompt:
                    return MockResponse("0.6")
                elif "太贵" in prompt:
                    return MockResponse("0.2")
                else:
                    return MockResponse("0.5")
            elif "满意度" in prompt:
                if "专业" in prompt:
                    return MockResponse("0.8")
                elif "不满" in prompt:
                    return MockResponse("0.3")
                else:
                    return MockResponse("0.6")
            
            # 客户回应
            else:
                return MockResponse("我想了解一下这个产品。")
    
    class MockResponse:
        def __init__(self, content):
            self.content = content
    
    return AdaptiveMockLLM()

def test_strategy_manager():
    """测试策略管理器"""
    print("🧠 测试智能销售策略管理器...")
    
    try:
        from utils.sales_strategy_manager import SalesStrategyManager
        
        mock_llm = create_adaptive_mock_llm()
        strategy_manager = SalesStrategyManager(mock_llm)
        
        print("✅ 策略管理器创建成功")
        
        # 测试不同的客户状态场景
        test_scenarios = [
            {
                "name": "高意向高满意度 - 积极成交",
                "purchase_intent": 0.8,
                "satisfaction": 0.7,
                "customer_message": "这款手镯很不错，我很喜欢",
                "expected_strategy": "积极成交"
            },
            {
                "name": "高意向低满意度 - 满意度提升",
                "purchase_intent": 0.8,
                "satisfaction": 0.3,
                "customer_message": "产品不错但服务有问题",
                "expected_strategy": "满意度提升"
            },
            {
                "name": "低意向高满意度 - 欲望激发",
                "purchase_intent": 0.2,
                "satisfaction": 0.7,
                "customer_message": "服务很好但我还在看看",
                "expected_strategy": "欲望激发"
            },
            {
                "name": "低意向低满意度 - 客户挽救",
                "purchase_intent": 0.2,
                "satisfaction": 0.2,
                "customer_message": "不太满意，考虑离开",
                "expected_strategy": "客户挽救"
            }
        ]
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n📋 测试场景 {i}: {scenario['name']}")
            print("-" * 50)
            
            strategy = strategy_manager.select_optimal_strategy(
                customer_type="预算敏感性",
                purchase_intent=scenario["purchase_intent"],
                satisfaction=scenario["satisfaction"],
                conversation_round=3,
                customer_message=scenario["customer_message"],
                conversation_history=[]
            )
            
            print(f"客户状态: 购买意向={scenario['purchase_intent']}, 满意度={scenario['satisfaction']}")
            print(f"客户消息: {scenario['customer_message']}")
            print(f"选择策略: {strategy['strategy']}")
            print(f"优先级: {strategy['priority']}")
            print(f"方法: {strategy['approach']}")
            print(f"紧急程度: {strategy['urgency']}")
            
            # 验证策略选择
            if strategy["strategy"] == scenario["expected_strategy"]:
                print("✅ 策略选择正确")
            else:
                print(f"⚠️ 策略选择可能需要调整 (期望: {scenario['expected_strategy']})")
            
            # 测试生成自适应回应
            response = strategy_manager.generate_adaptive_response(
                strategy=strategy,
                customer_type="预算敏感性",
                purchase_intent=scenario["purchase_intent"],
                satisfaction=scenario["satisfaction"],
                customer_message=scenario["customer_message"],
                conversation_history=[]
            )
            
            print(f"生成回应: {response[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 策略管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_adaptive_sales_agent():
    """测试自适应销售智能体"""
    print("\n🛍️ 测试自适应销售智能体...")
    
    try:
        from agents.sales_agent import SalesAgent
        from database.product_db import ProductDatabase
        
        mock_llm = create_adaptive_mock_llm()
        product_db = ProductDatabase("test_adaptive.db")
        sales_agent = SalesAgent(mock_llm, product_db)
        
        print("✅ 自适应销售智能体创建成功")
        
        # 模拟不同客户状态的销售场景
        test_cases = [
            {
                "customer_message": "这款手镯很漂亮，我很喜欢",
                "customer_status": {
                    "purchase_intent": 0.8,
                    "satisfaction": 0.7,
                    "customer_type": "追求独特设计型"
                },
                "expected_approach": "积极成交"
            },
            {
                "customer_message": "价格有点贵，而且服务态度不太好",
                "customer_status": {
                    "purchase_intent": 0.6,
                    "satisfaction": 0.3,
                    "customer_type": "预算敏感性"
                },
                "expected_approach": "满意度提升"
            }
        ]
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n📋 测试案例 {i}:")
            print("-" * 30)
            
            # 使用智能策略回应
            response = sales_agent.respond_to_customer(
                customer_message=case["customer_message"],
                customer_type=case["customer_status"]["customer_type"],
                customer_status=case["customer_status"]
            )
            
            print(f"客户消息: {case['customer_message']}")
            print(f"客户状态: 意向={case['customer_status']['purchase_intent']}, 满意度={case['customer_status']['satisfaction']}")
            print(f"销售回应: {response}")
            
            # 检查销售状态
            sales_status = sales_agent.get_sales_status()
            print(f"当前技巧: {sales_status['current_technique']}")
            print(f"销售方法: {sales_status['sales_approach']}")
            
            if "智能策略" in sales_status['current_technique']:
                print("✅ 成功使用智能自适应策略")
            else:
                print("⚠️ 可能未使用智能策略")
        
        # 清理测试数据库
        import os
        if os.path.exists("test_adaptive.db"):
            os.remove("test_adaptive.db")
        
        return True
        
    except Exception as e:
        print(f"❌ 自适应销售智能体测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_adaptive_workflow():
    """测试完整的自适应工作流"""
    print("\n🔄 测试完整的自适应销售工作流...")
    
    try:
        from simple_workflow import SimpleSalesWorkflow
        
        mock_llm = create_adaptive_mock_llm()
        workflow = SimpleSalesWorkflow(mock_llm, "test_workflow_adaptive.db")
        
        print("✅ 自适应工作流创建成功")
        
        # 运行模拟
        result = workflow.run_simulation("预算敏感性")
        
        print(f"✅ 模拟运行完成")
        print(f"📊 最终状态: {result['final_status']}")
        print(f"📊 对话轮数: {result['conversation_rounds']}")
        print(f"📊 成功: {result['success']}")
        
        # 检查销售策略分析
        sales_metrics = result["sales_final_metrics"]
        if "strategy_analysis" in sales_metrics:
            strategy_analysis = sales_metrics["strategy_analysis"]
            print(f"📊 策略使用情况: {strategy_analysis}")
            print("✅ 成功集成智能策略分析")
        
        # 显示部分对话
        print(f"\n💬 对话片段:")
        for i, message in enumerate(result["conversation"][:6], 1):
            print(f"{i}. {message}")
        
        # 清理测试文件
        import os
        if os.path.exists("test_workflow_adaptive.db"):
            os.remove("test_workflow_adaptive.db")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整工作流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🎯 智能自适应销售策略测试")
    print("=" * 60)
    print("测试销售智能体根据客户状态动态调整策略的能力")
    print("=" * 60)
    
    tests = [
        ("策略管理器", test_strategy_manager),
        ("自适应销售智能体", test_adaptive_sales_agent),
        ("完整自适应工作流", test_complete_adaptive_workflow)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 测试: {test_name}")
        print("=" * 40)
        if test_func():
            passed += 1
            print(f"✅ {test_name} 测试通过")
        else:
            print(f"❌ {test_name} 测试失败")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 智能自适应销售策略测试通过！")
        print("\n💡 新功能特点:")
        print("✅ 根据客户购买意向和满意度动态选择策略")
        print("✅ 五种核心策略：积极成交、满意度提升、欲望激发、客户挽救、渐进推进")
        print("✅ 智能话术生成，针对不同策略生成相应回应")
        print("✅ 策略使用历史分析和优化建议")
        print("✅ 紧急情况自动识别和处理")
        print("✅ 销售阶段智能判断和推进")
    else:
        print("⚠️ 部分测试失败，需要进一步调试。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
