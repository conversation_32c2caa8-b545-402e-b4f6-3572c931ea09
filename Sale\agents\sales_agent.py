"""
销售智能体实现
"""
from typing import Dict, List, Optional
from langchain.llms.base import LLM
from database.product_db import ProductDatabase, Product
from prompts.sales_prompts import SALES_TECHNIQUES, SALES_GREETING
from utils.sales_strategy_manager import SalesStrategyManager
from langchain_core.messages import HumanMessage
from langchain.prompts import PromptTemplate
# from workflows.sales_workflow import SalesState
import re

class SalesAgent:
    """销售智能体"""
    
    def __init__(self, llm: LLM, product_db: ProductDatabase):
        self.llm = llm
        self.product_db = product_db
        self.conversation_history = []
        self.customer_needs = {}
        self.current_technique = "需求探寻"
        self.recommended_products = []
        self.strategy_manager = SalesStrategyManager(llm)  # 智能策略管理器
        self.conversation_round = 0  # 对话轮数
        
    def get_greeting(self) -> str:
        """获取销售开场白"""
        # self.conversation_history.append(SALES_GREETING)
        return SALES_GREETING
    
    def analyze_customer_message(self, customer_message: str, customer_type: str) -> Dict:
        """分析客户消息，确定销售策略"""
        analysis = {
            "technique_needed": "需求探寻",
            "customer_intent": "探索",
            "objections": [],
            "buying_signals": []
        }
        
        # 检测购买信号
        buying_signals = ["买", "要", "购买", "成交", "这个不错", "就这个"]
        if any(signal in customer_message for signal in buying_signals):
            analysis["buying_signals"] = [s for s in buying_signals if s in customer_message]
            analysis["technique_needed"] = "成交缔结"
            analysis["customer_intent"] = "购买"
        
        # 检测异议
        objections = ["太贵", "不喜欢", "不合适", "质量", "款式"]
        detected_objections = [obj for obj in objections if obj in customer_message]
        if detected_objections:
            analysis["objections"] = detected_objections
            analysis["technique_needed"] = "异议处理"
            analysis["customer_intent"] = "异议"
        
        # 检测需求信息
        if "预算" in customer_message or "价格" in customer_message:
            self.customer_needs["budget_mentioned"] = True
        if "设计" in customer_message or "款式" in customer_message:
            self.customer_needs["design_mentioned"] = True
        if "送人" in customer_message or "自己" in customer_message:
            self.customer_needs["purpose_mentioned"] = True
        
        # 如果已经收集了基本需求信息，转向产品推荐
        if len(self.customer_needs) >= 2 and analysis["technique_needed"] == "需求探寻":
            analysis["technique_needed"] = "产品推荐"
            analysis["customer_intent"] = "了解产品"
        
        return analysis
    
    def respond_to_customer(self, customer_message: str, customer_type: str, customer_status: Dict = None) -> str:
        """响应客户消息（支持智能策略调整）"""
        # 更新对话轮数
        self.conversation_round += 1

        # 更新对话历史
        self.conversation_history.append(f"客户: {customer_message}")

        # 如果提供了客户状态，使用智能策略
        if customer_status:
            response = self._generate_adaptive_response(customer_message, customer_type, customer_status)
        else:
            # 使用传统方法
            analysis = self.analyze_customer_message(customer_message, customer_type)
            self.current_technique = analysis["technique_needed"]
            response = self._generate_response(customer_message, customer_type, analysis)

        # 更新对话历史
        self.conversation_history.append(f"销售员: {response}")

        return response

    def _generate_adaptive_response(self, customer_message: str, customer_type: str, customer_status: Dict) -> str:
        """生成自适应回应（基于客户状态）"""
        purchase_intent = customer_status.get("purchase_intent", 0.5)
        satisfaction = customer_status.get("satisfaction", 0.5)

        # 选择最优策略
        strategy = self.strategy_manager.select_optimal_strategy(
            customer_type=customer_type,
            purchase_intent=purchase_intent,
            satisfaction=satisfaction,
            conversation_round=self.conversation_round,
            customer_message=customer_message,
            conversation_history=self.conversation_history
        )

        # 更新当前技巧
        self.current_technique = f"智能策略: {strategy['strategy']}"

        # 生成自适应回应
        response = self.strategy_manager.generate_adaptive_response(
            strategy=strategy,
            customer_type=customer_type,
            purchase_intent=purchase_intent,
            satisfaction=satisfaction,
            customer_message=customer_message,
            conversation_history=self.conversation_history,
            recommended_products=self.recommended_products,
            current_stage=self._determine_sales_stage(purchase_intent, satisfaction)
        )

        return response

    def _determine_sales_stage(self, purchase_intent: float, satisfaction: float) -> str:
        """确定当前销售阶段"""
        if purchase_intent >= 0.7:
            return "成交阶段"
        elif purchase_intent >= 0.5:
            return "说服阶段"
        elif satisfaction >= 0.6:
            return "需求激发阶段"
        else:
            return "关系建立阶段"
    
    def _generate_response(self, customer_message: str, customer_type: str, analysis: Dict) -> str:
        """根据分析结果生成回应"""
        technique = analysis["technique_needed"]
        prompt_template = SALES_TECHNIQUES[technique]
        # prompt_template = PromptTemplate.from_template(prompt_template)
        # 构建对话历史字符串
        history_str = "\n".join(self.conversation_history[-6:])  # 最近6轮对话
        
        if technique == "需求探寻":
            products = self.product_db.get_all_products()
            prompt = prompt_template.format(
                customer_type=customer_type,
                conversation_history=history_str,
                customer_message=customer_message,
                products = products
            )
        
        elif technique == "产品推荐":
            # 获取推荐产品
            products = self._get_recommended_products(customer_type)
            products_str = self._format_products_for_prompt(products)
            
            prompt = prompt_template.format(
                customer_type=customer_type,
                customer_needs=str(self.customer_needs),
                available_products=products_str,
                conversation_history=history_str,
                customer_message=customer_message
            )
        
        elif technique == "异议处理":
            objection = ", ".join(analysis["objections"])
            prompt = prompt_template.format(
                customer_type=customer_type,
                customer_objection=objection,
                conversation_history=history_str
            )
        
        elif technique == "成交缔结":
            buying_signals = ", ".join(analysis["buying_signals"])
            recommended_product = self.recommended_products[0] if self.recommended_products else "当前推荐产品"
            
            prompt = prompt_template.format(
                customer_type=customer_type,
                buying_signals=buying_signals,
                recommended_product=recommended_product,
                conversation_history=history_str,
                customer_message=customer_message
            )
        
        # 生成回应
        response = self.llm.invoke(prompt).content.replace('"','')
        return response
    
    def _get_recommended_products(self, customer_type: str) -> List[Product]:
        """获取推荐产品"""
        # 根据客户类型获取产品
        products = self.product_db.get_products_by_customer_type(customer_type)
        
        # 如果客户提到了预算，进一步筛选
        if "budget_mentioned" in self.customer_needs:
            # 这里可以根据对话历史提取具体预算数字
            # 简化处理：根据客户类型设定默认预算范围
            if customer_type == "预算敏感性":
                products = [p for p in products if p.price <= 2000]
            elif customer_type == "犹豫不决型":
                products = [p for p in products if p.price <= 3000]
        
        # 保存推荐产品
        self.recommended_products = products[:3]  # 最多推荐3个
        return self.recommended_products
    
    def _format_products_for_prompt(self, products: List[Product]) -> str:
        """格式化产品信息用于prompt"""
        if not products:
            return "暂无合适产品"
        
        formatted = []
        for product in products:
            formatted.append(
                f"产品名称: {product.name}\n"
                f"价格: {product.price}元\n"
                f"设计描述: {product.design_description}\n"
                f"材质: {product.material}\n"
                f"特点: {product.features}\n"
                f"库存: {product.stock}件\n"
            )
        
        return "\n---\n".join(formatted)
    
    def get_sales_status(self) -> Dict:
        """获取销售状态"""
        strategy_analysis = self.strategy_manager.get_strategy_analysis()

        return {
            "current_technique": self.current_technique,
            "customer_needs_identified": len(self.customer_needs),
            "products_recommended": len(self.recommended_products),
            "conversation_rounds": self.conversation_round,
            "strategy_analysis": strategy_analysis,
            "sales_approach": "智能自适应策略"
        }
