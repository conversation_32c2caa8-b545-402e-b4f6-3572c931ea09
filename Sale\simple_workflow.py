"""
简化的销售工作流实现 - 支持智能策略调整
"""
from typing import Dict, Any, List
from agents.customer_agents import CustomerAgent, create_customer_agent
from agents.sales_agent import SalesAgent
from database.product_db import ProductDatabase
import config

class SimpleSalesWorkflow:
    """简化的销售工作流管理器"""
    
    def __init__(self, llm, db_path: str):
        self.llm = llm
        self.product_db = ProductDatabase(db_path)
    
    def run_simulation(self, customer_type: str) -> Dict[str, Any]:
        """运行销售模拟"""
        # 初始化智能体
        customer_agent = create_customer_agent(customer_type, self.llm)
        sales_agent = SalesAgent(self.llm, self.product_db)
        
        # 对话历史
        conversation = []
        conversation_round = 0
        sales_status = "active"
        
        # 销售员开场白
        sales_greeting = sales_agent.get_greeting()
        conversation.append(f"销售员: {sales_greeting}")
        
        # 客户初始回应
        customer_greeting = customer_agent.get_initial_message()
        conversation.append(f"客户: {customer_greeting}")
        conversation_round = 1
        
        # 对话循环
        max_rounds = getattr(config, 'MAX_CONVERSATION_ROUNDS', 10)
        
        while conversation_round < max_rounds and sales_status == "active":
            try:
                # 销售员回应（使用智能策略）
                last_customer_message = conversation[-1]
                if last_customer_message.startswith("客户: "):
                    customer_content = last_customer_message[3:]  # 移除"客户: "前缀
                    
                    # 获取客户状态用于智能策略调整
                    customer_status = customer_agent.get_status()
                    
                    sales_response = sales_agent.respond_to_customer(
                        customer_content, 
                        customer_type,
                        customer_status  # 传递客户状态进行智能策略调整
                    )
                    conversation.append(f"销售员: {sales_response}")
                
                # 检查是否应该结束
                if self._should_end_conversation(customer_agent, conversation):
                    break
                
                # 客户回应
                last_sales_message = conversation[-1]
                if last_sales_message.startswith("销售员: "):
                    sales_content = last_sales_message[4:]  # 移除"销售员: "前缀
                    
                    customer_response = customer_agent.respond_to_sales(sales_content)
                    conversation.append(f"客户: {customer_response}")
                
                conversation_round += 1
                
                # 评估状态
                sales_status = self._evaluate_status(customer_agent, conversation, conversation_round)
                
            except Exception as e:
                print(f"对话过程中出现错误: {e}")
                sales_status = "failed"
                break
        
        # 添加结束消息
        if sales_status == "success":
            final_message = "销售员: 太好了！感谢您的购买，我们会为您安排最好的服务。"
        elif sales_status == "failed":
            final_message = "销售员: 没关系，如果您以后有需要，随时欢迎您再来看看。"
        else:
            final_message = "销售员: 今天的时间差不多了，如果您还有疑问，欢迎随时联系我们。"
        
        conversation.append(final_message)
        
        # 返回结果
        return {
            "customer_type": customer_type,
            "conversation": conversation,
            "final_status": sales_status,
            "conversation_rounds": conversation_round,
            "customer_final_status": customer_agent.get_status(),
            "sales_final_metrics": sales_agent.get_sales_status(),
            "success": sales_status == "success"
        }
    
    def _should_end_conversation(self, customer_agent: CustomerAgent, conversation: List[str]) -> bool:
        """判断是否应该结束对话"""
        # 检查客户是否准备购买
        if customer_agent.is_ready_to_buy():
            return True
        
        # 检查客户是否已经流失
        if customer_agent.is_lost():
            return True
        
        return False
    
    def _evaluate_status(self, customer_agent: CustomerAgent, conversation: List[str], round_num: int) -> str:
        """评估对话状态"""
        # 检查成功条件
        if customer_agent.is_ready_to_buy():
            # 检查最后的客户消息是否包含购买意向
            success_keywords = getattr(config, 'SUCCESS_KEYWORDS', ["我买了", "我要这个", "成交", "购买"])
            if len(conversation) >= 2:
                last_customer_message = conversation[-1]
                if any(keyword in last_customer_message for keyword in success_keywords):
                    return "success"
        
        # 检查失败条件
        if customer_agent.is_lost():
            return "failed"
        
        # 检查最大轮数
        max_rounds = getattr(config, 'MAX_CONVERSATION_ROUNDS', 10)
        if round_num >= max_rounds:
            return "max_rounds"
        
        # 继续对话
        return "active"

# 为了兼容性，创建一个别名
SalesWorkflow = SimpleSalesWorkflow
