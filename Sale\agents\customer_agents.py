"""
客户智能体实现
"""
from typing import Dict, List
from langchain.llms.base import LLM
from langchain.schema import BaseMessage, HumanMessage, AIMessage
from prompts.customer_prompts import CUSTOMER_PROMPTS, CUSTOMER_GREETINGS
import random

class CustomerAgent:
    """客户智能体基类"""
    
    def __init__(self, customer_type: str, llm: LLM):
        self.customer_type = customer_type
        self.llm = llm
        self.conversation_history = []
        self.purchase_intent = 0.0  # 购买意向 0-1
        self.satisfaction = 0.5     # 满意度 0-1
        
    def get_initial_message(self) -> str:
        """获取客户初始问候语"""
        return CUSTOMER_GREETINGS.get(self.customer_type, "你好，我想看看黄金手镯。")
    
    def respond_to_sales(self, sales_message: str) -> str:
        """响应销售员的话"""
        # 更新对话历史
        self.conversation_history.append(f"销售员: {sales_message}")
        
        # 构建对话历史字符串
        history_str = "\n".join(self.conversation_history[-5:])  # 只保留最近5轮对话
        
        # 获取客户类型对应的prompt
        prompt_template = CUSTOMER_PROMPTS[self.customer_type]
        
        # 填充prompt
        prompt = prompt_template.format(
            conversation_history=history_str,
            sales_message=sales_message
        )
        
        # 生成回应
        response = self.llm.invoke(prompt)
        
        # 更新对话历史
        self.conversation_history.append(f"客户: {response}")
        
        # 更新购买意向和满意度
        self._update_intent_and_satisfaction(sales_message, response)
        
        return response
    
    def _update_intent_and_satisfaction(self, sales_message: str, customer_response: str):
        """更新购买意向和满意度"""
        # 简单的规则基础更新逻辑
        positive_keywords = ["好的", "不错", "喜欢", "考虑", "可以"]
        negative_keywords = ["不", "太贵", "不喜欢", "算了", "再看看"]
        purchase_keywords = ["买", "要", "购买", "成交"]
        
        # 更新满意度
        if any(word in customer_response for word in positive_keywords):
            self.satisfaction = min(1.0, self.satisfaction + 0.1)
        elif any(word in customer_response for word in negative_keywords):
            self.satisfaction = max(0.0, self.satisfaction - 0.1)
        
        # 更新购买意向
        if any(word in customer_response for word in purchase_keywords):
            self.purchase_intent = min(1.0, self.purchase_intent + 0.3)
        elif any(word in customer_response for word in negative_keywords):
            self.purchase_intent = max(0.0, self.purchase_intent - 0.2)
        
        # 基于客户类型调整
        if self.customer_type == "预算敏感性":
            if "价格" in sales_message or "便宜" in sales_message:
                self.satisfaction += 0.05
        elif self.customer_type == "追求独特设计型":
            if "设计" in sales_message or "工艺" in sales_message:
                self.satisfaction += 0.05
        elif self.customer_type == "犹豫不决型":
            if "推荐" in sales_message or "建议" in sales_message:
                self.satisfaction += 0.05
    
    def is_ready_to_buy(self) -> bool:
        """判断是否准备购买"""
        return self.purchase_intent > 0.7 and self.satisfaction > 0.6
    
    def is_lost(self) -> bool:
        """判断是否已经流失"""
        return self.purchase_intent < 0.2 or self.satisfaction < 0.3
    
    def get_status(self) -> Dict:
        """获取客户状态"""
        return {
            "customer_type": self.customer_type,
            "purchase_intent": self.purchase_intent,
            "satisfaction": self.satisfaction,
            "conversation_rounds": len(self.conversation_history) // 2
        }

class BudgetSensitiveCustomer(CustomerAgent):
    """预算敏感型客户"""
    
    def __init__(self, llm: LLM):
        super().__init__("预算敏感性", llm)
        self.budget_range = (800, 2000)
        self.price_sensitivity = 0.8
    
    def _update_intent_and_satisfaction(self, sales_message: str, customer_response: str):
        super()._update_intent_and_satisfaction(sales_message, customer_response)
        
        # 价格敏感特殊逻辑
        if "折扣" in sales_message or "优惠" in sales_message:
            self.purchase_intent += 0.1
            self.satisfaction += 0.1

class DesignFocusedCustomer(CustomerAgent):
    """追求独特设计型客户"""
    
    def __init__(self, llm: LLM):
        super().__init__("追求独特设计型", llm)
        self.budget_range = (2000, 6000)
        self.design_preference = "独特"
    
    def _update_intent_and_satisfaction(self, sales_message: str, customer_response: str):
        super()._update_intent_and_satisfaction(sales_message, customer_response)
        
        # 设计导向特殊逻辑
        if "独特" in sales_message or "限量" in sales_message or "工艺" in sales_message:
            self.purchase_intent += 0.1
            self.satisfaction += 0.1

class HesitantCustomer(CustomerAgent):
    """犹豫不决型客户"""
    
    def __init__(self, llm: LLM):
        super().__init__("犹豫不决型", llm)
        self.budget_range = (1000, 3000)
        self.decision_difficulty = 0.8
    
    def _update_intent_and_satisfaction(self, sales_message: str, customer_response: str):
        super()._update_intent_and_satisfaction(sales_message, customer_response)
        
        # 犹豫不决特殊逻辑
        if "推荐" in sales_message or "保证" in sales_message or "退换" in sales_message:
            self.purchase_intent += 0.1
            self.satisfaction += 0.1

def create_customer_agent(customer_type: str, llm: LLM) -> CustomerAgent:
    """工厂函数：创建客户智能体"""
    if customer_type == "预算敏感性":
        return BudgetSensitiveCustomer(llm)
    elif customer_type == "追求独特设计型":
        return DesignFocusedCustomer(llm)
    elif customer_type == "犹豫不决型":
        return HesitantCustomer(llm)
    else:
        raise ValueError(f"未知的客户类型: {customer_type}")
