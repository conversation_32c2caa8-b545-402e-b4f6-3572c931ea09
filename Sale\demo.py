"""
销售模拟演示脚本
"""
import os
import sys
from langchain.llms import OpenAI
from workflows.sales_workflow import SalesWorkflow
from utils.conversation_manager import ConversationManager
import config

class MockLLM:
    """模拟LLM用于演示"""
    
    def __init__(self):
        self.customer_responses = {
            "预算敏感性": [
                "你好，我想看看黄金手镯，有什么性价比高的推荐吗？",
                "这个价格怎么样？有没有更便宜一点的？",
                "1500元左右的有吗？我预算不是很多。",
                "这款看起来不错，能便宜点吗？",
                "好的，我考虑一下这款。"
            ],
            "追求独特设计型": [
                "你好，我在找一些设计独特的黄金手镯，有什么特别的款式吗？",
                "这个设计有什么特别之处？工艺怎么样？",
                "有没有限量款或者可以定制的？",
                "这个花丝工艺很精美，我很喜欢。",
                "就要这款花丝工艺手镯了。"
            ],
            "犹豫不决型": [
                "你好，我想买个黄金手镯，但不太确定选哪款，你能帮我推荐一下吗？",
                "你觉得哪款更适合我？我有点选择困难。",
                "如果买了不合适怎么办？可以退换吗？",
                "你推荐哪个？我真的不知道怎么选。",
                "好吧，我相信你的推荐，就要这个。"
            ]
        }
        
        self.sales_responses = [
            "欢迎光临！我是您的专属黄金手镯顾问。我们这里有各种款式的黄金手镯，从经典款到限量设计款都有。请问您今天是想为自己选购还是送人呢？",
            "根据您的需求，我为您推荐几款特别适合的产品。",
            "我理解您的考虑，让我为您详细介绍一下这款产品的优势。",
            "这确实是一个很好的选择，我们提供完善的售后保障。",
            "太好了！感谢您的购买，我们会为您安排最好的服务。"
        ]
        
        self.response_index = 0
        self.current_customer_type = None
    
    def invoke(self, prompt: str) -> str:
        """模拟LLM响应"""
        # 简单的响应逻辑
        if "客户" in prompt and self.current_customer_type:
            responses = self.customer_responses.get(self.current_customer_type, ["好的"])
            if self.response_index < len(responses):
                response = responses[self.response_index]
                self.response_index += 1
                return response
            else:
                return "我买了！"
        else:
            # 销售员响应
            if self.response_index < len(self.sales_responses):
                return self.sales_responses[min(self.response_index, len(self.sales_responses) - 1)]
            else:
                return "感谢您的购买！"

def run_demo():
    """运行演示"""
    print("🛍️ 黄金手镯销售模拟系统 - 演示模式")
    print("=" * 60)
    print("注意: 这是演示模式，使用模拟的LLM响应")
    print("=" * 60)
    
    # 创建模拟LLM
    mock_llm = MockLLM()
    
    # 初始化组件
    workflow = SalesWorkflow(mock_llm, config.DATABASE_PATH)
    conversation_manager = ConversationManager("demo_logs.json")
    
    # 客户类型
    customer_types = ["预算敏感性", "追求独特设计型", "犹豫不决型"]
    
    print("\n🎭 开始演示三种客户类型的销售场景...\n")
    
    for i, customer_type in enumerate(customer_types, 1):
        print(f"\n{'='*20} 场景 {i}: {customer_type} {'='*20}")
        
        # 设置当前客户类型
        mock_llm.current_customer_type = customer_type
        mock_llm.response_index = 0
        
        try:
            # 运行模拟
            result = workflow.run_simulation(customer_type)
            
            # 显示对话
            conversation_manager.print_conversation(result["conversation"])
            
            # 显示结果
            status = "✅ 成功交易" if result["success"] else "❌ 未成交"
            print(f"📊 模拟结果: {status}")
            print(f"🔄 对话轮数: {result['conversation_rounds']}")
            
            # 保存结果
            conversation_manager.save_conversation(result)
            
        except Exception as e:
            print(f"❌ 演示失败: {e}")
        
        if i < len(customer_types):
            input("\n按回车键继续下一个场景...")
    
    print("\n" + "="*60)
    print("🎉 演示完成！")
    
    # 显示统计
    conversation_manager.print_statistics()

def show_system_architecture():
    """显示系统架构"""
    print("\n🏗️ 系统架构说明")
    print("=" * 60)
    
    architecture = """
📁 项目结构:
├── config.py              # 配置文件
├── requirements.txt       # 依赖包
├── main.py               # 主程序入口
├── demo.py               # 演示脚本
├── database/
│   └── product_db.py     # 产品数据库管理
├── agents/
│   ├── customer_agents.py # 客户智能体
│   └── sales_agent.py    # 销售智能体
├── workflows/
│   └── sales_workflow.py # LangGraph工作流
├── prompts/
│   ├── customer_prompts.py # 客户对话模板
│   └── sales_prompts.py   # 销售技巧模板
└── utils/
    └── conversation_manager.py # 对话管理

🧠 核心组件:

1. 客户智能体 (Customer Agents)
   - 预算敏感型: 关注价格和性价比
   - 追求独特设计型: 关注设计和工艺
   - 犹豫不决型: 需要建议和保证

2. 销售智能体 (Sales Agent)
   - 需求探寻: 了解客户需求
   - 产品推荐: 匹配合适产品
   - 异议处理: 解决客户疑虑
   - 成交缔结: 促成交易

3. LangGraph工作流
   - 状态管理: 跟踪对话状态
   - 流程控制: 管理对话流程
   - 条件判断: 决定下一步行动

4. 产品数据库
   - SQLite数据库存储产品信息
   - 支持按类型、价格筛选
   - 库存管理

5. 对话管理
   - 记录对话历史
   - 统计分析
   - 性能评估
"""
    
    print(architecture)

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--architecture":
        show_system_architecture()
    else:
        run_demo()
